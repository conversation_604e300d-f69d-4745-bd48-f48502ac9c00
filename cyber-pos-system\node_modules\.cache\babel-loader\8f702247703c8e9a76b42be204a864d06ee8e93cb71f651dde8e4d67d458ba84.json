{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\services\\\\ServiceStats.tsx\";\nimport React from 'react';\nimport { BarChart3, TrendingUp, DollarSign, Activity, Tag, ToggleRight, ToggleLeft } from 'lucide-react';\nimport { getServiceCategoriesWithCounts, formatPrice } from '../../utils/serviceUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ServiceStats = ({\n  services\n}) => {\n  const totalServices = services.length;\n  const activeServices = services.filter(s => s.isActive).length;\n  const inactiveServices = totalServices - activeServices;\n  const averagePrice = services.length > 0 ? services.reduce((sum, service) => sum + service.basePrice, 0) / services.length : 0;\n  const highestPrice = services.length > 0 ? Math.max(...services.map(s => s.basePrice)) : 0;\n  const lowestPrice = services.length > 0 ? Math.min(...services.map(s => s.basePrice)) : 0;\n  const categoriesWithCounts = getServiceCategoriesWithCounts(services);\n  const totalCategories = categoriesWithCounts.length;\n  const servicesWithBundles = services.filter(s => s.bundledServices && s.bundledServices.length > 0).length;\n  const servicesWithPriceOverride = services.filter(s => s.allowPriceOverride).length;\n  const stats = [{\n    name: 'Total Services',\n    value: totalServices.toString(),\n    icon: Activity,\n    color: 'text-blue-600',\n    bgColor: 'bg-blue-100'\n  }, {\n    name: 'Active Services',\n    value: activeServices.toString(),\n    icon: ToggleRight,\n    color: 'text-green-600',\n    bgColor: 'bg-green-100'\n  }, {\n    name: 'Categories',\n    value: totalCategories.toString(),\n    icon: Tag,\n    color: 'text-purple-600',\n    bgColor: 'bg-purple-100'\n  }, {\n    name: 'Average Price',\n    value: formatPrice(averagePrice),\n    icon: DollarSign,\n    color: 'text-yellow-600',\n    bgColor: 'bg-yellow-100'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n      children: stats.map(stat => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-2 rounded-lg ${stat.bgColor}`,\n            children: /*#__PURE__*/_jsxDEV(stat.icon, {\n              className: `h-5 w-5 ${stat.color}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: stat.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)\n      }, stat.name, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), \"Service Status\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ToggleRight, {\n                className: \"h-4 w-4 text-green-500 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Active Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: activeServices\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ToggleLeft, {\n                className: \"h-4 w-4 text-gray-400 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Inactive Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: inactiveServices\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pt-2 border-t\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Activation Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-semibold text-green-600\",\n                children: [totalServices > 0 ? Math.round(activeServices / totalServices * 100) : 0, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), \"Pricing Analysis\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Highest Price\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: formatPrice(highestPrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Lowest Price\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: formatPrice(lowestPrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Average Price\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: formatPrice(averagePrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pt-2 border-t\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Price Override Allowed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-semibold text-blue-600\",\n                children: servicesWithPriceOverride\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Tag, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), \"Categories\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: categoriesWithCounts.length > 0 ? categoriesWithCounts.map(({\n            category,\n            count,\n            activeCount\n          }) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500\",\n                children: [activeCount, \"/\", count]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 bg-gray-200 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-600 h-2 rounded-full\",\n                  style: {\n                    width: `${count > 0 ? activeCount / count * 100 : 0}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this)]\n          }, category, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 text-center py-4\",\n            children: \"No categories yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), \"Service Features\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Services with Bundles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: servicesWithBundles\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Price Override Enabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: servicesWithPriceOverride\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pt-2 border-t\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Bundle Usage\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-semibold text-purple-600\",\n                children: [totalServices > 0 ? Math.round(servicesWithBundles / totalServices * 100) : 0, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_c = ServiceStats;\nexport default ServiceStats;\nvar _c;\n$RefreshReg$(_c, \"ServiceStats\");", "map": {"version": 3, "names": ["React", "BarChart3", "TrendingUp", "DollarSign", "Activity", "Tag", "ToggleRight", "ToggleLeft", "getServiceCategoriesWithCounts", "formatPrice", "jsxDEV", "_jsxDEV", "ServiceStats", "services", "totalServices", "length", "activeServices", "filter", "s", "isActive", "inactiveServices", "averagePrice", "reduce", "sum", "service", "basePrice", "highestPrice", "Math", "max", "map", "lowestPrice", "min", "categoriesWithCounts", "totalCategories", "servicesWithBundles", "bundledServices", "servicesWithPriceOverride", "allowPriceOverride", "stats", "name", "value", "toString", "icon", "color", "bgColor", "className", "children", "stat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "round", "category", "count", "activeCount", "style", "width", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/services/ServiceStats.tsx"], "sourcesContent": ["import React from 'react';\nimport { \n  BarChart3, \n  TrendingUp, \n  DollarSign, \n  Activity,\n  Tag,\n  ToggleRight,\n  ToggleLeft\n} from 'lucide-react';\nimport { Service } from '../../types';\nimport { getServiceCategoriesWithCounts, formatPrice } from '../../utils/serviceUtils';\n\ninterface ServiceStatsProps {\n  services: Service[];\n}\n\nconst ServiceStats: React.FC<ServiceStatsProps> = ({ services }) => {\n  const totalServices = services.length;\n  const activeServices = services.filter(s => s.isActive).length;\n  const inactiveServices = totalServices - activeServices;\n  \n  const averagePrice = services.length > 0 \n    ? services.reduce((sum, service) => sum + service.basePrice, 0) / services.length \n    : 0;\n  \n  const highestPrice = services.length > 0 \n    ? Math.max(...services.map(s => s.basePrice)) \n    : 0;\n  \n  const lowestPrice = services.length > 0 \n    ? Math.min(...services.map(s => s.basePrice)) \n    : 0;\n  \n  const categoriesWithCounts = getServiceCategoriesWithCounts(services);\n  const totalCategories = categoriesWithCounts.length;\n  \n  const servicesWithBundles = services.filter(s => s.bundledServices && s.bundledServices.length > 0).length;\n  const servicesWithPriceOverride = services.filter(s => s.allowPriceOverride).length;\n\n  const stats = [\n    {\n      name: 'Total Services',\n      value: totalServices.toString(),\n      icon: Activity,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-100'\n    },\n    {\n      name: 'Active Services',\n      value: activeServices.toString(),\n      icon: ToggleRight,\n      color: 'text-green-600',\n      bgColor: 'bg-green-100'\n    },\n    {\n      name: 'Categories',\n      value: totalCategories.toString(),\n      icon: Tag,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-100'\n    },\n    {\n      name: 'Average Price',\n      value: formatPrice(averagePrice),\n      icon: DollarSign,\n      color: 'text-yellow-600',\n      bgColor: 'bg-yellow-100'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Main Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {stats.map((stat) => (\n          <div key={stat.name} className=\"bg-white rounded-lg shadow p-4\">\n            <div className=\"flex items-center\">\n              <div className={`p-2 rounded-lg ${stat.bgColor}`}>\n                <stat.icon className={`h-5 w-5 ${stat.color}`} />\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-500\">{stat.name}</p>\n                <p className=\"text-lg font-semibold text-gray-900\">{stat.value}</p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Detailed Stats */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Service Status Breakdown */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <BarChart3 className=\"h-5 w-5 mr-2\" />\n            Service Status\n          </h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <ToggleRight className=\"h-4 w-4 text-green-500 mr-2\" />\n                <span className=\"text-sm text-gray-600\">Active Services</span>\n              </div>\n              <span className=\"text-sm font-semibold text-gray-900\">{activeServices}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <ToggleLeft className=\"h-4 w-4 text-gray-400 mr-2\" />\n                <span className=\"text-sm text-gray-600\">Inactive Services</span>\n              </div>\n              <span className=\"text-sm font-semibold text-gray-900\">{inactiveServices}</span>\n            </div>\n            <div className=\"pt-2 border-t\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium text-gray-700\">Activation Rate</span>\n                <span className=\"text-sm font-semibold text-green-600\">\n                  {totalServices > 0 ? Math.round((activeServices / totalServices) * 100) : 0}%\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Pricing Analysis */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <DollarSign className=\"h-5 w-5 mr-2\" />\n            Pricing Analysis\n          </h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Highest Price</span>\n              <span className=\"text-sm font-semibold text-gray-900\">{formatPrice(highestPrice)}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Lowest Price</span>\n              <span className=\"text-sm font-semibold text-gray-900\">{formatPrice(lowestPrice)}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Average Price</span>\n              <span className=\"text-sm font-semibold text-gray-900\">{formatPrice(averagePrice)}</span>\n            </div>\n            <div className=\"pt-2 border-t\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Price Override Allowed</span>\n                <span className=\"text-sm font-semibold text-blue-600\">{servicesWithPriceOverride}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Category Breakdown */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <Tag className=\"h-5 w-5 mr-2\" />\n            Categories\n          </h3>\n          <div className=\"space-y-2\">\n            {categoriesWithCounts.length > 0 ? (\n              categoriesWithCounts.map(({ category, count, activeCount }) => (\n                <div key={category} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                  <span className=\"text-sm font-medium text-gray-700\">{category}</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-xs text-gray-500\">{activeCount}/{count}</span>\n                    <div className=\"w-16 bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-blue-600 h-2 rounded-full\" \n                        style={{ width: `${count > 0 ? (activeCount / count) * 100 : 0}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <p className=\"text-sm text-gray-500 text-center py-4\">No categories yet</p>\n            )}\n          </div>\n        </div>\n\n        {/* Service Features */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <TrendingUp className=\"h-5 w-5 mr-2\" />\n            Service Features\n          </h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Services with Bundles</span>\n              <span className=\"text-sm font-semibold text-gray-900\">{servicesWithBundles}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Price Override Enabled</span>\n              <span className=\"text-sm font-semibold text-gray-900\">{servicesWithPriceOverride}</span>\n            </div>\n            <div className=\"pt-2 border-t\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium text-gray-700\">Bundle Usage</span>\n                <span className=\"text-sm font-semibold text-purple-600\">\n                  {totalServices > 0 ? Math.round((servicesWithBundles / totalServices) * 100) : 0}%\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ServiceStats;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,UAAU,EACVC,UAAU,EACVC,QAAQ,EACRC,GAAG,EACHC,WAAW,EACXC,UAAU,QACL,cAAc;AAErB,SAASC,8BAA8B,EAAEC,WAAW,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMvF,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAClE,MAAMC,aAAa,GAAGD,QAAQ,CAACE,MAAM;EACrC,MAAMC,cAAc,GAAGH,QAAQ,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAACJ,MAAM;EAC9D,MAAMK,gBAAgB,GAAGN,aAAa,GAAGE,cAAc;EAEvD,MAAMK,YAAY,GAAGR,QAAQ,CAACE,MAAM,GAAG,CAAC,GACpCF,QAAQ,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,CAACC,SAAS,EAAE,CAAC,CAAC,GAAGZ,QAAQ,CAACE,MAAM,GAC/E,CAAC;EAEL,MAAMW,YAAY,GAAGb,QAAQ,CAACE,MAAM,GAAG,CAAC,GACpCY,IAAI,CAACC,GAAG,CAAC,GAAGf,QAAQ,CAACgB,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACO,SAAS,CAAC,CAAC,GAC3C,CAAC;EAEL,MAAMK,WAAW,GAAGjB,QAAQ,CAACE,MAAM,GAAG,CAAC,GACnCY,IAAI,CAACI,GAAG,CAAC,GAAGlB,QAAQ,CAACgB,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACO,SAAS,CAAC,CAAC,GAC3C,CAAC;EAEL,MAAMO,oBAAoB,GAAGxB,8BAA8B,CAACK,QAAQ,CAAC;EACrE,MAAMoB,eAAe,GAAGD,oBAAoB,CAACjB,MAAM;EAEnD,MAAMmB,mBAAmB,GAAGrB,QAAQ,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACiB,eAAe,IAAIjB,CAAC,CAACiB,eAAe,CAACpB,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;EAC1G,MAAMqB,yBAAyB,GAAGvB,QAAQ,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACmB,kBAAkB,CAAC,CAACtB,MAAM;EAEnF,MAAMuB,KAAK,GAAG,CACZ;IACEC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE1B,aAAa,CAAC2B,QAAQ,CAAC,CAAC;IAC/BC,IAAI,EAAEtC,QAAQ;IACduC,KAAK,EAAE,eAAe;IACtBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAExB,cAAc,CAACyB,QAAQ,CAAC,CAAC;IAChCC,IAAI,EAAEpC,WAAW;IACjBqC,KAAK,EAAE,gBAAgB;IACvBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAEP,eAAe,CAACQ,QAAQ,CAAC,CAAC;IACjCC,IAAI,EAAErC,GAAG;IACTsC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE/B,WAAW,CAACY,YAAY,CAAC;IAChCqB,IAAI,EAAEvC,UAAU;IAChBwC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE;EACX,CAAC,CACF;EAED,oBACEjC,OAAA;IAAKkC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBnC,OAAA;MAAKkC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClER,KAAK,CAACT,GAAG,CAAEkB,IAAI,iBACdpC,OAAA;QAAqBkC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7DnC,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCnC,OAAA;YAAKkC,SAAS,EAAE,kBAAkBE,IAAI,CAACH,OAAO,EAAG;YAAAE,QAAA,eAC/CnC,OAAA,CAACoC,IAAI,CAACL,IAAI;cAACG,SAAS,EAAE,WAAWE,IAAI,CAACJ,KAAK;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNxC,OAAA;YAAKkC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA;cAAGkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEC,IAAI,CAACR;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChExC,OAAA;cAAGkC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAEC,IAAI,CAACP;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GATEJ,IAAI,CAACR,IAAI;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNxC,OAAA;MAAKkC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDnC,OAAA;QAAKkC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CnC,OAAA;UAAIkC,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACtEnC,OAAA,CAACV,SAAS;YAAC4C,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxC,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnC,OAAA;YAAKkC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDnC,OAAA;cAAKkC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnC,OAAA,CAACL,WAAW;gBAACuC,SAAS,EAAC;cAA6B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDxC,OAAA;gBAAMkC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNxC,OAAA;cAAMkC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAE9B;YAAc;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eACNxC,OAAA;YAAKkC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDnC,OAAA;cAAKkC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnC,OAAA,CAACJ,UAAU;gBAACsC,SAAS,EAAC;cAA4B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrDxC,OAAA;gBAAMkC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACNxC,OAAA;cAAMkC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAE1B;YAAgB;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,eACNxC,OAAA;YAAKkC,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BnC,OAAA;cAAKkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnC,OAAA;gBAAMkC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1ExC,OAAA;gBAAMkC,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,GACnDhC,aAAa,GAAG,CAAC,GAAGa,IAAI,CAACyB,KAAK,CAAEpC,cAAc,GAAGF,aAAa,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GAC9E;cAAA;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxC,OAAA;QAAKkC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CnC,OAAA;UAAIkC,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACtEnC,OAAA,CAACR,UAAU;YAAC0C,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxC,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnC,OAAA;YAAKkC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDnC,OAAA;cAAMkC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5DxC,OAAA;cAAMkC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAErC,WAAW,CAACiB,YAAY;YAAC;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,eACNxC,OAAA;YAAKkC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDnC,OAAA;cAAMkC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DxC,OAAA;cAAMkC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAErC,WAAW,CAACqB,WAAW;YAAC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eACNxC,OAAA;YAAKkC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDnC,OAAA;cAAMkC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5DxC,OAAA;cAAMkC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAErC,WAAW,CAACY,YAAY;YAAC;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,eACNxC,OAAA;YAAKkC,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BnC,OAAA;cAAKkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnC,OAAA;gBAAMkC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrExC,OAAA;gBAAMkC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAEV;cAAyB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxC,OAAA;QAAKkC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CnC,OAAA;UAAIkC,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACtEnC,OAAA,CAACN,GAAG;YAACwC,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxC,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBd,oBAAoB,CAACjB,MAAM,GAAG,CAAC,GAC9BiB,oBAAoB,CAACH,GAAG,CAAC,CAAC;YAAEwB,QAAQ;YAAEC,KAAK;YAAEC;UAAY,CAAC,kBACxD5C,OAAA;YAAoBkC,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtFnC,OAAA;cAAMkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEO;YAAQ;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrExC,OAAA;cAAKkC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CnC,OAAA;gBAAMkC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAES,WAAW,EAAC,GAAC,EAACD,KAAK;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpExC,OAAA;gBAAKkC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChDnC,OAAA;kBACEkC,SAAS,EAAC,8BAA8B;kBACxCW,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAGH,KAAK,GAAG,CAAC,GAAIC,WAAW,GAAGD,KAAK,GAAI,GAAG,GAAG,CAAC;kBAAI;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAVEE,QAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWb,CACN,CAAC,gBAEFxC,OAAA;YAAGkC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC3E;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxC,OAAA;QAAKkC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CnC,OAAA;UAAIkC,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACtEnC,OAAA,CAACT,UAAU;YAAC2C,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxC,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnC,OAAA;YAAKkC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDnC,OAAA;cAAMkC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpExC,OAAA;cAAMkC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAEZ;YAAmB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACNxC,OAAA;YAAKkC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDnC,OAAA;cAAMkC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrExC,OAAA;cAAMkC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAEV;YAAyB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,eACNxC,OAAA;YAAKkC,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BnC,OAAA;cAAKkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnC,OAAA;gBAAMkC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvExC,OAAA;gBAAMkC,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,GACpDhC,aAAa,GAAG,CAAC,GAAGa,IAAI,CAACyB,KAAK,CAAElB,mBAAmB,GAAGpB,aAAa,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACnF;cAAA;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACO,EAAA,GA/LI9C,YAAyC;AAiM/C,eAAeA,YAAY;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}