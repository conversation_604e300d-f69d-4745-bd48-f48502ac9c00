import { useState, useEffect } from 'react';
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Service } from '../types';

export const useServices = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Real-time listener for services
  useEffect(() => {
    const servicesQuery = query(
      collection(db, 'services'),
      orderBy('category'),
      orderBy('name')
    );

    const unsubscribe = onSnapshot(
      servicesQuery,
      (snapshot) => {
        const servicesData: Service[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          servicesData.push({
            id: doc.id,
            name: data.name || '',
            description: data.description || '',
            basePrice: data.basePrice || 0,
            category: data.category || '',
            isActive: data.isActive !== false,
            allowPriceOverride: data.allowPriceOverride !== false,
            bundledServices: data.bundledServices || [],
            createdAt: data.createdAt?.toDate() || new Date(),
            updatedAt: data.updatedAt?.toDate() || new Date(),
          });
        });
        setServices(servicesData);
        setLoading(false);
        setError(null);
      },
      (error) => {
        console.error('Error fetching services:', error);
        setError('Failed to fetch services');
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, []);

  const createService = async (serviceData: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setError(null);
      await addDoc(collection(db, 'services'), {
        ...serviceData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error creating service:', error);
      setError('Failed to create service');
      throw error;
    }
  };

  const updateService = async (serviceId: string, updates: Partial<Service>) => {
    try {
      setError(null);
      const { id, createdAt, ...updateData } = updates;
      await updateDoc(doc(db, 'services', serviceId), {
        ...updateData,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating service:', error);
      setError('Failed to update service');
      throw error;
    }
  };

  const deleteService = async (serviceId: string) => {
    try {
      setError(null);
      await deleteDoc(doc(db, 'services', serviceId));
    } catch (error) {
      console.error('Error deleting service:', error);
      setError('Failed to delete service');
      throw error;
    }
  };

  const getServiceById = async (serviceId: string): Promise<Service | null> => {
    try {
      const serviceDoc = await getDoc(doc(db, 'services', serviceId));
      if (serviceDoc.exists()) {
        const data = serviceDoc.data();
        return {
          id: serviceDoc.id,
          name: data.name || '',
          description: data.description || '',
          basePrice: data.basePrice || 0,
          category: data.category || '',
          isActive: data.isActive !== false,
          allowPriceOverride: data.allowPriceOverride !== false,
          bundledServices: data.bundledServices || [],
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        };
      }
      return null;
    } catch (error) {
      console.error('Error fetching service:', error);
      throw error;
    }
  };

  const getServicesByCategory = (category: string) => {
    return services.filter(service => service.category === category && service.isActive);
  };

  const getActiveServices = () => {
    return services.filter(service => service.isActive);
  };

  const getServiceCategories = () => {
    const categories = [...new Set(services.map(service => service.category))];
    return categories.sort();
  };

  return {
    services,
    loading,
    error,
    createService,
    updateService,
    deleteService,
    getServiceById,
    getServicesByCategory,
    getActiveServices,
    getServiceCategories,
  };
};
