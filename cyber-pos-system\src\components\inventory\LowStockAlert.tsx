import React from 'react';
import {
  AlertTriangle,
  Calendar,
  Package,
  RefreshCw,
  TrendingDown,
  Clock
} from 'lucide-react';
import { Product } from '../../types';

interface LowStockAlertProps {
  lowStockProducts: Product[];
  expiringProducts: Product[];
  onStockAdjust: (product: Product) => void;
}

const LowStockAlert: React.FC<LowStockAlertProps> = ({
  lowStockProducts,
  expiringProducts,
  onStockAdjust
}) => {
  const outOfStockProducts = lowStockProducts.filter(p => p.stockQuantity === 0);
  const lowButNotOutProducts = lowStockProducts.filter(p => p.stockQuantity > 0);

  const getDaysUntilExpiry = (expiryDate: Date) => {
    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getExpiryUrgency = (daysUntilExpiry: number) => {
    if (daysUntilExpiry <= 7) return 'critical';
    if (daysUntilExpiry <= 14) return 'warning';
    return 'notice';
  };

  const getExpiryColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'text-red-600 bg-red-100 border-red-200';
      case 'warning': return 'text-orange-600 bg-orange-100 border-orange-200';
      default: return 'text-yellow-600 bg-yellow-100 border-yellow-200';
    }
  };

  if (lowStockProducts.length === 0 && expiringProducts.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-8 text-center">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
          <Package className="h-8 w-8 text-green-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">All Good!</h3>
        <p className="text-gray-600">
          No low stock or expiring products at the moment. Your inventory is well managed.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-red-100">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Out of Stock</p>
              <p className="text-lg font-semibold text-gray-900">{outOfStockProducts.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-orange-100">
              <TrendingDown className="h-5 w-5 text-orange-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Low Stock</p>
              <p className="text-lg font-semibold text-gray-900">{lowButNotOutProducts.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-yellow-100">
              <Calendar className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Expiring Soon</p>
              <p className="text-lg font-semibold text-gray-900">{expiringProducts.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Out of Stock Products */}
      {outOfStockProducts.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
              Out of Stock ({outOfStockProducts.length})
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              These products are completely out of stock and need immediate restocking.
            </p>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              {outOfStockProducts.map((product) => (
                <div key={product.id} className="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{product.name}</h4>
                    <p className="text-sm text-gray-600">{product.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm">
                      <span className="text-gray-500">Category: {product.category}</span>
                      <span className="text-gray-500">Reorder Level: {product.reorderLevel}</span>
                      <span className="text-green-600 font-medium">Price: KSh {product.price.toLocaleString()}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800">
                      Out of Stock
                    </span>
                    <button
                      onClick={() => onStockAdjust(product)}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      Restock
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Low Stock Products */}
      {lowButNotOutProducts.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <TrendingDown className="h-5 w-5 text-orange-600 mr-2" />
              Low Stock ({lowButNotOutProducts.length})
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              These products are running low and should be restocked soon.
            </p>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              {lowButNotOutProducts.map((product) => (
                <div key={product.id} className="flex items-center justify-between p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{product.name}</h4>
                    <p className="text-sm text-gray-600">{product.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm">
                      <span className="text-gray-500">Category: {product.category}</span>
                      <span className="text-orange-600 font-medium">
                        Stock: {product.stockQuantity} / Reorder: {product.reorderLevel}
                      </span>
                      <span className="text-green-600 font-medium">Price: KSh {product.price.toLocaleString()}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-orange-100 text-orange-800">
                      Low Stock
                    </span>
                    <button
                      onClick={() => onStockAdjust(product)}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      Adjust
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Expiring Products */}
      {expiringProducts.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Calendar className="h-5 w-5 text-yellow-600 mr-2" />
              Expiring Soon ({expiringProducts.length})
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              These products are approaching their expiry dates and need attention.
            </p>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              {expiringProducts.map((product) => {
                const daysUntilExpiry = getDaysUntilExpiry(product.expiryDate!);
                const urgency = getExpiryUrgency(daysUntilExpiry);
                const colorClass = getExpiryColor(urgency);

                return (
                  <div key={product.id} className={`flex items-center justify-between p-4 border rounded-lg ${colorClass}`}>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{product.name}</h4>
                      <p className="text-sm text-gray-600">{product.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-sm">
                        <span className="text-gray-500">Category: {product.category}</span>
                        <span className="text-gray-500">Stock: {product.stockQuantity} units</span>
                        <span className="text-green-600 font-medium">Price: KSh {product.price.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="text-right">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          <span className="text-sm font-medium">
                            {daysUntilExpiry > 0 ? `${daysUntilExpiry} days` : 'Expired'}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {product.expiryDate!.toLocaleDateString()}
                        </div>
                      </div>
                      <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                        urgency === 'critical' ? 'bg-red-100 text-red-800' :
                        urgency === 'warning' ? 'bg-orange-100 text-orange-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {urgency === 'critical' ? 'Critical' :
                         urgency === 'warning' ? 'Warning' : 'Notice'}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Action Recommendations */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-3">Recommended Actions</h3>
        <div className="space-y-2 text-sm text-blue-800">
          {outOfStockProducts.length > 0 && (
            <p>• Immediately restock {outOfStockProducts.length} out-of-stock product{outOfStockProducts.length > 1 ? 's' : ''}</p>
          )}
          {lowButNotOutProducts.length > 0 && (
            <p>• Plan restocking for {lowButNotOutProducts.length} low-stock product{lowButNotOutProducts.length > 1 ? 's' : ''}</p>
          )}
          {expiringProducts.length > 0 && (
            <p>• Review pricing or promotions for {expiringProducts.length} expiring product{expiringProducts.length > 1 ? 's' : ''}</p>
          )}
          <p>• Consider adjusting reorder levels based on sales patterns</p>
          <p>• Review supplier lead times to prevent stockouts</p>
        </div>
      </div>
    </div>
  );
};

export default LowStockAlert;
