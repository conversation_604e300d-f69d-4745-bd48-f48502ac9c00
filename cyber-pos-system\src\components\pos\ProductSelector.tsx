import React, { useState } from 'react';
import {
  Plus,
  Package,
  AlertTriangle,
  Calendar,
  ShoppingCart,
  Minus
} from 'lucide-react';
import { Product } from '../../types';

interface ProductSelectorProps {
  products: Product[];
  loading: boolean;
  viewMode: 'grid' | 'list';
  onAddToCart: (product: Product, quantity: number) => void;
  cart: {
    isInCart: (itemId: string, type: 'service' | 'product') => boolean;
    getItemQuantity: (itemId: string, type: 'service' | 'product') => number;
  };
}

const ProductSelector: React.FC<ProductSelectorProps> = ({
  products,
  loading,
  viewMode,
  onAddToCart,
  cart
}) => {
  const [quantities, setQuantities] = useState<Record<string, number>>({});

  const handleQuantityChange = (productId: string, quantity: number) => {
    setQuantities(prev => ({
      ...prev,
      [productId]: Math.max(1, quantity)
    }));
  };

  const handleAddToCart = (product: Product) => {
    const quantity = quantities[product.id] || 1;
    onAddToCart(product, quantity);
    // Reset quantity after adding
    setQuantities(prev => ({ ...prev, [product.id]: 1 }));
  };

  const getQuantity = (productId: string) => quantities[productId] || 1;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-gray-600">Loading products...</span>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <Package className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
        <p className="mt-1 text-sm text-gray-500">
          Try adjusting your search or filter criteria, or check if products are in stock.
        </p>
      </div>
    );
  }

  if (viewMode === 'list') {
    return (
      <div className="space-y-2">
        {products.map((product) => (
          <ProductListItem
            key={product.id}
            product={product}
            quantity={getQuantity(product.id)}
            onQuantityChange={(quantity) => handleQuantityChange(product.id, quantity)}
            onAddToCart={() => handleAddToCart(product)}
            isInCart={cart.isInCart(product.id, 'product')}
            cartQuantity={cart.getItemQuantity(product.id, 'product')}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {products.map((product) => (
        <ProductCard
          key={product.id}
          product={product}
          quantity={getQuantity(product.id)}
          onQuantityChange={(quantity) => handleQuantityChange(product.id, quantity)}
          onAddToCart={() => handleAddToCart(product)}
          isInCart={cart.isInCart(product.id, 'product')}
          cartQuantity={cart.getItemQuantity(product.id, 'product')}
        />
      ))}
    </div>
  );
};

// Product Card Component
interface ProductItemProps {
  product: Product;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
  onAddToCart: () => void;
  isInCart: boolean;
  cartQuantity: number;
}

const ProductCard: React.FC<ProductItemProps> = ({
  product,
  quantity,
  onQuantityChange,
  onAddToCart,
  isInCart,
  cartQuantity
}) => {
  const isLowStock = product.stockQuantity <= product.reorderLevel;
  const isExpiringSoon = product.hasExpiry && product.expiryDate && 
    product.expiryDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900">{product.name}</h3>
          <p className="text-sm text-gray-600 mt-1">{product.description}</p>
          <div className="flex items-center space-x-2 mt-2">
            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
              {product.category}
            </span>
            {isLowStock && (
              <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Low Stock
              </span>
            )}
          </div>
        </div>
        {isInCart && (
          <div className="flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
            <ShoppingCart className="h-3 w-3 mr-1" />
            {cartQuantity}
          </div>
        )}
      </div>

      {/* Stock and Expiry Info */}
      <div className="mb-3 space-y-1">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">In Stock:</span>
          <span className={`font-medium ${isLowStock ? 'text-red-600' : 'text-green-600'}`}>
            {product.stockQuantity} units
          </span>
        </div>
        {product.hasExpiry && product.expiryDate && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Expires:</span>
            <span className={`font-medium ${isExpiringSoon ? 'text-orange-600' : 'text-gray-900'}`}>
              {product.expiryDate.toLocaleDateString()}
              {isExpiringSoon && (
                <Calendar className="h-3 w-3 inline ml-1" />
              )}
            </span>
          </div>
        )}
      </div>

      {/* Price */}
      <div className="mb-4">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Price:</span>
          <span className="font-semibold text-green-600">
            KSh {product.price.toLocaleString()}
          </span>
        </div>
      </div>

      {/* Quantity Selector */}
      <div className="mb-4">
        <label className="block text-sm text-gray-600 mb-2">Quantity:</label>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => onQuantityChange(quantity - 1)}
            disabled={quantity <= 1}
            className="p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Minus className="h-4 w-4" />
          </button>
          <span className="w-12 text-center font-medium">{quantity}</span>
          <button
            onClick={() => onQuantityChange(quantity + 1)}
            disabled={quantity >= product.stockQuantity}
            className="p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>
        <div className="text-xs text-gray-500 mt-1">
          Total: KSh {(product.price * quantity).toLocaleString()}
        </div>
      </div>

      {/* Add to Cart Button */}
      <button
        onClick={onAddToCart}
        disabled={quantity > product.stockQuantity}
        className="w-full bg-primary-600 text-white py-2 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
      >
        <Plus className="h-4 w-4 mr-2" />
        Add to Cart
      </button>
    </div>
  );
};

// Product List Item Component (for list view)
const ProductListItem: React.FC<ProductItemProps> = ({
  product,
  quantity,
  onQuantityChange,
  onAddToCart,
  isInCart,
  cartQuantity
}) => {
  const isLowStock = product.stockQuantity <= product.reorderLevel;
  const isExpiringSoon = product.hasExpiry && product.expiryDate && 
    product.expiryDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 flex items-center space-x-4">
      <div className="flex-1">
        <div className="flex items-center space-x-3 mb-2">
          <h3 className="font-semibold text-gray-900">{product.name}</h3>
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
            {product.category}
          </span>
          {isLowStock && (
            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Low Stock
            </span>
          )}
          {isInCart && (
            <div className="flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
              <ShoppingCart className="h-3 w-3 mr-1" />
              {cartQuantity}
            </div>
          )}
        </div>
        <p className="text-sm text-gray-600">{product.description}</p>
        <div className="flex items-center space-x-4 mt-2 text-sm">
          <span className={`${isLowStock ? 'text-red-600' : 'text-green-600'}`}>
            Stock: {product.stockQuantity}
          </span>
          {product.hasExpiry && product.expiryDate && (
            <span className={`${isExpiringSoon ? 'text-orange-600' : 'text-gray-600'}`}>
              Expires: {product.expiryDate.toLocaleDateString()}
            </span>
          )}
        </div>
      </div>

      <div className="flex items-center space-x-4">
        {/* Price */}
        <div className="text-right">
          <div className="font-semibold text-green-600">
            KSh {product.price.toLocaleString()}
          </div>
          <div className="text-xs text-gray-500">
            per unit
          </div>
        </div>

        {/* Quantity Selector */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onQuantityChange(quantity - 1)}
            disabled={quantity <= 1}
            className="p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50"
          >
            <Minus className="h-3 w-3" />
          </button>
          <span className="w-8 text-center text-sm font-medium">{quantity}</span>
          <button
            onClick={() => onQuantityChange(quantity + 1)}
            disabled={quantity >= product.stockQuantity}
            className="p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50"
          >
            <Plus className="h-3 w-3" />
          </button>
        </div>

        {/* Add to Cart Button */}
        <button
          onClick={onAddToCart}
          disabled={quantity > product.stockQuantity}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50 flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add
        </button>
      </div>
    </div>
  );
};

export default ProductSelector;
