import { Service } from '../types';

export interface ServiceCalculation {
  service: Service;
  originalPrice: number;
  finalPrice: number;
  bundledServices: Service[];
  bundledValue: number;
  discount: number;
}

/**
 * Calculate the effective price of a service including bundled services
 */
export const calculateServicePrice = (
  service: Service,
  allServices: Service[],
  customPrice?: number
): ServiceCalculation => {
  const originalPrice = customPrice ?? service.basePrice;
  
  // Get bundled services
  const bundledServices = service.bundledServices
    ?.map(id => allServices.find(s => s.id === id))
    .filter(Boolean) as Service[] || [];
  
  // Calculate bundled value (services that come free)
  const bundledValue = bundledServices.reduce((total, bundledService) => {
    return total + bundledService.basePrice;
  }, 0);
  
  return {
    service,
    originalPrice,
    finalPrice: originalPrice, // Main service price doesn't change
    bundledServices,
    bundledValue,
    discount: bundledValue // The value of free services is the discount
  };
};

/**
 * Calculate total for multiple services, considering bundles
 */
export const calculateServicesTotal = (
  selectedServices: Array<{ service: Service; customPrice?: number; quantity?: number }>,
  allServices: Service[]
): {
  items: Array<ServiceCalculation & { quantity: number }>;
  subtotal: number;
  totalBundledValue: number;
  totalDiscount: number;
  finalTotal: number;
} => {
  const items = selectedServices.map(({ service, customPrice, quantity = 1 }) => {
    const calculation = calculateServicePrice(service, allServices, customPrice);
    return { ...calculation, quantity };
  });
  
  const subtotal = items.reduce((total, item) => {
    return total + (item.finalPrice * item.quantity);
  }, 0);
  
  const totalBundledValue = items.reduce((total, item) => {
    return total + (item.bundledValue * item.quantity);
  }, 0);
  
  return {
    items,
    subtotal,
    totalBundledValue,
    totalDiscount: totalBundledValue,
    finalTotal: subtotal
  };
};

/**
 * Get services by category with active filter
 */
export const getServicesByCategory = (
  services: Service[],
  category?: string,
  activeOnly: boolean = true
): Service[] => {
  return services.filter(service => {
    const matchesCategory = !category || service.category === category;
    const matchesActive = !activeOnly || service.isActive;
    return matchesCategory && matchesActive;
  });
};

/**
 * Search services by name or description
 */
export const searchServices = (
  services: Service[],
  searchTerm: string,
  activeOnly: boolean = true
): Service[] => {
  if (!searchTerm.trim()) return activeOnly ? services.filter(s => s.isActive) : services;
  
  const term = searchTerm.toLowerCase();
  return services.filter(service => {
    const matchesSearch = 
      service.name.toLowerCase().includes(term) ||
      service.description.toLowerCase().includes(term) ||
      service.category.toLowerCase().includes(term);
    const matchesActive = !activeOnly || service.isActive;
    return matchesSearch && matchesActive;
  });
};

/**
 * Get popular services based on usage (placeholder for now)
 */
export const getPopularServices = (
  services: Service[],
  limit: number = 5
): Service[] => {
  // For now, return active services sorted by name
  // In a real app, this would be based on transaction data
  return services
    .filter(service => service.isActive)
    .sort((a, b) => a.name.localeCompare(b.name))
    .slice(0, limit);
};

/**
 * Validate service data
 */
export const validateService = (service: Partial<Service>): string[] => {
  const errors: string[] = [];
  
  if (!service.name?.trim()) {
    errors.push('Service name is required');
  }
  
  if (!service.category?.trim()) {
    errors.push('Category is required');
  }
  
  if (service.basePrice === undefined || service.basePrice < 0) {
    errors.push('Base price must be a positive number');
  }
  
  if (service.name && service.name.length > 100) {
    errors.push('Service name must be less than 100 characters');
  }
  
  if (service.description && service.description.length > 500) {
    errors.push('Description must be less than 500 characters');
  }
  
  return errors;
};

/**
 * Format price for display
 */
export const formatPrice = (price: number, currency: string = 'KSh'): string => {
  return `${currency} ${price.toLocaleString()}`;
};

/**
 * Get service categories with counts
 */
export const getServiceCategoriesWithCounts = (services: Service[]): Array<{
  category: string;
  count: number;
  activeCount: number;
}> => {
  const categoryMap = new Map<string, { total: number; active: number }>();
  
  services.forEach(service => {
    const current = categoryMap.get(service.category) || { total: 0, active: 0 };
    current.total++;
    if (service.isActive) current.active++;
    categoryMap.set(service.category, current);
  });
  
  return Array.from(categoryMap.entries())
    .map(([category, counts]) => ({
      category,
      count: counts.total,
      activeCount: counts.active
    }))
    .sort((a, b) => a.category.localeCompare(b.category));
};
