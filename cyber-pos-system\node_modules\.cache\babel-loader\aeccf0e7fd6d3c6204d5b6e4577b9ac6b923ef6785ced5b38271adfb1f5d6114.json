{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { collection, doc, addDoc, getDoc, serverTimestamp, runTransaction, writeBatch } from 'firebase/firestore';\nimport { db } from '../config/firebase';\nexport const useTransactions = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Create or update customer for credit sales\n  const createOrUpdateCustomer = async (customerName, customerPhone) => {\n    try {\n      // Check if customer already exists by name\n      const customersRef = collection(db, 'customers');\n      const customerData = {\n        name: customerName.trim(),\n        phone: customerPhone === null || customerPhone === void 0 ? void 0 : customerPhone.trim(),\n        totalDebt: 0,\n        isActive: true,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      // For simplicity, create a new customer document\n      // In production, you might want to search for existing customers first\n      const customerDoc = await addDoc(customersRef, {\n        ...customerData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp()\n      });\n      return customerDoc.id;\n    } catch (error) {\n      console.error('Error creating customer:', error);\n      throw new Error('Failed to create customer record');\n    }\n  };\n\n  // Update product stock quantities\n  const updateProductStock = async items => {\n    const batch = writeBatch(db);\n    for (const item of items) {\n      if (item.type === 'product') {\n        const productRef = doc(db, 'products', item.itemId);\n        const productDoc = await getDoc(productRef);\n        if (productDoc.exists()) {\n          const currentStock = productDoc.data().stockQuantity || 0;\n          const newStock = Math.max(0, currentStock - item.quantity);\n          batch.update(productRef, {\n            stockQuantity: newStock,\n            updatedAt: serverTimestamp()\n          });\n        }\n      }\n    }\n    await batch.commit();\n  };\n\n  // Save transaction to Firebase\n  const saveTransaction = async (cartState, paymentData, attendantId) => {\n    setLoading(true);\n    setError(null);\n    try {\n      // Prepare transaction items\n      const transactionItems = cartState.items.map(item => ({\n        id: item.id,\n        type: item.type,\n        itemId: item.itemId,\n        name: item.name,\n        quantity: item.quantity,\n        unitPrice: item.price,\n        totalPrice: item.price * item.quantity,\n        notes: item.notes\n      }));\n\n      // Handle customer creation for credit sales\n      let customerId;\n      if (paymentData.paymentMethod === 'debt' && paymentData.customerName) {\n        customerId = await createOrUpdateCustomer(paymentData.customerName, paymentData.customerPhone);\n      }\n\n      // Prepare transaction data\n      const transactionData = {\n        items: transactionItems,\n        subtotal: cartState.subtotal,\n        discount: cartState.discount,\n        total: cartState.total,\n        paymentMethod: paymentData.paymentMethod,\n        customerId,\n        attendantId,\n        status: 'completed',\n        notes: paymentData.notes,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      // Use Firestore transaction to ensure data consistency\n      const transactionId = await runTransaction(db, async transaction => {\n        // Create the transaction document\n        const transactionRef = doc(collection(db, 'transactions'));\n        transaction.set(transactionRef, {\n          ...transactionData,\n          createdAt: serverTimestamp(),\n          updatedAt: serverTimestamp()\n        });\n\n        // Update customer debt if it's a credit sale\n        if (customerId && paymentData.paymentMethod === 'debt') {\n          const customerRef = doc(db, 'customers', customerId);\n          const customerDoc = await transaction.get(customerRef);\n          if (customerDoc.exists()) {\n            const currentDebt = customerDoc.data().totalDebt || 0;\n            transaction.update(customerRef, {\n              totalDebt: currentDebt + cartState.total,\n              updatedAt: serverTimestamp()\n            });\n          }\n        }\n        return transactionRef.id;\n      });\n\n      // Update product stock (outside of the transaction for better performance)\n      await updateProductStock(transactionItems);\n      return transactionId;\n    } catch (error) {\n      console.error('Error saving transaction:', error);\n      setError('Failed to save transaction');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n  return {\n    saveTransaction,\n    loading,\n    error\n  };\n};\n_s(useTransactions, \"Iz3ozxQ+abMaAIcGIvU8cKUcBeo=\");", "map": {"version": 3, "names": ["useState", "collection", "doc", "addDoc", "getDoc", "serverTimestamp", "runTransaction", "writeBatch", "db", "useTransactions", "_s", "loading", "setLoading", "error", "setError", "createOrUpdateCustomer", "customerName", "customerPhone", "customersRef", "customerData", "name", "trim", "phone", "totalDebt", "isActive", "createdAt", "Date", "updatedAt", "customerDoc", "id", "console", "Error", "updateProductStock", "items", "batch", "item", "type", "productRef", "itemId", "productDoc", "exists", "currentStock", "data", "stockQuantity", "newStock", "Math", "max", "quantity", "update", "commit", "saveTransaction", "cartState", "paymentData", "attendantId", "transactionItems", "map", "unitPrice", "price", "totalPrice", "notes", "customerId", "paymentMethod", "transactionData", "subtotal", "discount", "total", "status", "transactionId", "transaction", "transactionRef", "set", "customerRef", "get", "currentDebt"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/hooks/useTransactions.ts"], "sourcesContent": ["import { useState } from 'react';\nimport {\n  collection,\n  doc,\n  addDoc,\n  updateDoc,\n  getDoc,\n  setDoc,\n  serverTimestamp,\n  runTransaction,\n  writeBatch\n} from 'firebase/firestore';\nimport { db } from '../config/firebase';\nimport { Transaction, TransactionItem, Customer, PaymentMethod, Product } from '../types';\nimport { CartState } from './useCart';\n\nexport const useTransactions = () => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Create or update customer for credit sales\n  const createOrUpdateCustomer = async (customerName: string, customerPhone?: string): Promise<string> => {\n    try {\n      // Check if customer already exists by name\n      const customersRef = collection(db, 'customers');\n      const customerData: Omit<Customer, 'id'> = {\n        name: customerName.trim(),\n        phone: customerPhone?.trim(),\n        totalDebt: 0,\n        isActive: true,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      // For simplicity, create a new customer document\n      // In production, you might want to search for existing customers first\n      const customerDoc = await addDoc(customersRef, {\n        ...customerData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n      });\n\n      return customerDoc.id;\n    } catch (error) {\n      console.error('Error creating customer:', error);\n      throw new Error('Failed to create customer record');\n    }\n  };\n\n  // Update product stock quantities\n  const updateProductStock = async (items: TransactionItem[]) => {\n    const batch = writeBatch(db);\n    \n    for (const item of items) {\n      if (item.type === 'product') {\n        const productRef = doc(db, 'products', item.itemId);\n        const productDoc = await getDoc(productRef);\n        \n        if (productDoc.exists()) {\n          const currentStock = productDoc.data().stockQuantity || 0;\n          const newStock = Math.max(0, currentStock - item.quantity);\n          \n          batch.update(productRef, {\n            stockQuantity: newStock,\n            updatedAt: serverTimestamp(),\n          });\n        }\n      }\n    }\n    \n    await batch.commit();\n  };\n\n  // Save transaction to Firebase\n  const saveTransaction = async (\n    cartState: CartState,\n    paymentData: {\n      paymentMethod: PaymentMethod;\n      customerName?: string;\n      customerPhone?: string;\n      notes?: string;\n    },\n    attendantId: string\n  ): Promise<string> => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      // Prepare transaction items\n      const transactionItems: TransactionItem[] = cartState.items.map(item => ({\n        id: item.id,\n        type: item.type,\n        itemId: item.itemId,\n        name: item.name,\n        quantity: item.quantity,\n        unitPrice: item.price,\n        totalPrice: item.price * item.quantity,\n        notes: item.notes,\n      }));\n\n      // Handle customer creation for credit sales\n      let customerId: string | undefined;\n      if (paymentData.paymentMethod === 'debt' && paymentData.customerName) {\n        customerId = await createOrUpdateCustomer(paymentData.customerName, paymentData.customerPhone);\n      }\n\n      // Prepare transaction data\n      const transactionData: Omit<Transaction, 'id'> = {\n        items: transactionItems,\n        subtotal: cartState.subtotal,\n        discount: cartState.discount,\n        total: cartState.total,\n        paymentMethod: paymentData.paymentMethod,\n        customerId,\n        attendantId,\n        status: 'completed',\n        notes: paymentData.notes,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      // Use Firestore transaction to ensure data consistency\n      const transactionId = await runTransaction(db, async (transaction) => {\n        // Create the transaction document\n        const transactionRef = doc(collection(db, 'transactions'));\n        transaction.set(transactionRef, {\n          ...transactionData,\n          createdAt: serverTimestamp(),\n          updatedAt: serverTimestamp(),\n        });\n\n        // Update customer debt if it's a credit sale\n        if (customerId && paymentData.paymentMethod === 'debt') {\n          const customerRef = doc(db, 'customers', customerId);\n          const customerDoc = await transaction.get(customerRef);\n          \n          if (customerDoc.exists()) {\n            const currentDebt = customerDoc.data().totalDebt || 0;\n            transaction.update(customerRef, {\n              totalDebt: currentDebt + cartState.total,\n              updatedAt: serverTimestamp(),\n            });\n          }\n        }\n\n        return transactionRef.id;\n      });\n\n      // Update product stock (outside of the transaction for better performance)\n      await updateProductStock(transactionItems);\n\n      return transactionId;\n    } catch (error) {\n      console.error('Error saving transaction:', error);\n      setError('Failed to save transaction');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    saveTransaction,\n    loading,\n    error,\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SACEC,UAAU,EACVC,GAAG,EACHC,MAAM,EAENC,MAAM,EAENC,eAAe,EACfC,cAAc,EACdC,UAAU,QACL,oBAAoB;AAC3B,SAASC,EAAE,QAAQ,oBAAoB;AAIvC,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAMe,sBAAsB,GAAG,MAAAA,CAAOC,YAAoB,EAAEC,aAAsB,KAAsB;IACtG,IAAI;MACF;MACA,MAAMC,YAAY,GAAGjB,UAAU,CAACO,EAAE,EAAE,WAAW,CAAC;MAChD,MAAMW,YAAkC,GAAG;QACzCC,IAAI,EAAEJ,YAAY,CAACK,IAAI,CAAC,CAAC;QACzBC,KAAK,EAAEL,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEI,IAAI,CAAC,CAAC;QAC5BE,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;MACtB,CAAC;;MAED;MACA;MACA,MAAME,WAAW,GAAG,MAAMzB,MAAM,CAACe,YAAY,EAAE;QAC7C,GAAGC,YAAY;QACfM,SAAS,EAAEpB,eAAe,CAAC,CAAC;QAC5BsB,SAAS,EAAEtB,eAAe,CAAC;MAC7B,CAAC,CAAC;MAEF,OAAOuB,WAAW,CAACC,EAAE;IACvB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAM,IAAIkB,KAAK,CAAC,kCAAkC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAG,MAAOC,KAAwB,IAAK;IAC7D,MAAMC,KAAK,GAAG3B,UAAU,CAACC,EAAE,CAAC;IAE5B,KAAK,MAAM2B,IAAI,IAAIF,KAAK,EAAE;MACxB,IAAIE,IAAI,CAACC,IAAI,KAAK,SAAS,EAAE;QAC3B,MAAMC,UAAU,GAAGnC,GAAG,CAACM,EAAE,EAAE,UAAU,EAAE2B,IAAI,CAACG,MAAM,CAAC;QACnD,MAAMC,UAAU,GAAG,MAAMnC,MAAM,CAACiC,UAAU,CAAC;QAE3C,IAAIE,UAAU,CAACC,MAAM,CAAC,CAAC,EAAE;UACvB,MAAMC,YAAY,GAAGF,UAAU,CAACG,IAAI,CAAC,CAAC,CAACC,aAAa,IAAI,CAAC;UACzD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGN,IAAI,CAACY,QAAQ,CAAC;UAE1Db,KAAK,CAACc,MAAM,CAACX,UAAU,EAAE;YACvBM,aAAa,EAAEC,QAAQ;YACvBjB,SAAS,EAAEtB,eAAe,CAAC;UAC7B,CAAC,CAAC;QACJ;MACF;IACF;IAEA,MAAM6B,KAAK,CAACe,MAAM,CAAC,CAAC;EACtB,CAAC;;EAED;EACA,MAAMC,eAAe,GAAG,MAAAA,CACtBC,SAAoB,EACpBC,WAKC,EACDC,WAAmB,KACC;IACpBzC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAMwC,gBAAmC,GAAGH,SAAS,CAAClB,KAAK,CAACsB,GAAG,CAACpB,IAAI,KAAK;QACvEN,EAAE,EAAEM,IAAI,CAACN,EAAE;QACXO,IAAI,EAAED,IAAI,CAACC,IAAI;QACfE,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBlB,IAAI,EAAEe,IAAI,CAACf,IAAI;QACf2B,QAAQ,EAAEZ,IAAI,CAACY,QAAQ;QACvBS,SAAS,EAAErB,IAAI,CAACsB,KAAK;QACrBC,UAAU,EAAEvB,IAAI,CAACsB,KAAK,GAAGtB,IAAI,CAACY,QAAQ;QACtCY,KAAK,EAAExB,IAAI,CAACwB;MACd,CAAC,CAAC,CAAC;;MAEH;MACA,IAAIC,UAA8B;MAClC,IAAIR,WAAW,CAACS,aAAa,KAAK,MAAM,IAAIT,WAAW,CAACpC,YAAY,EAAE;QACpE4C,UAAU,GAAG,MAAM7C,sBAAsB,CAACqC,WAAW,CAACpC,YAAY,EAAEoC,WAAW,CAACnC,aAAa,CAAC;MAChG;;MAEA;MACA,MAAM6C,eAAwC,GAAG;QAC/C7B,KAAK,EAAEqB,gBAAgB;QACvBS,QAAQ,EAAEZ,SAAS,CAACY,QAAQ;QAC5BC,QAAQ,EAAEb,SAAS,CAACa,QAAQ;QAC5BC,KAAK,EAAEd,SAAS,CAACc,KAAK;QACtBJ,aAAa,EAAET,WAAW,CAACS,aAAa;QACxCD,UAAU;QACVP,WAAW;QACXa,MAAM,EAAE,WAAW;QACnBP,KAAK,EAAEP,WAAW,CAACO,KAAK;QACxBlC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;MACtB,CAAC;;MAED;MACA,MAAMyC,aAAa,GAAG,MAAM7D,cAAc,CAACE,EAAE,EAAE,MAAO4D,WAAW,IAAK;QACpE;QACA,MAAMC,cAAc,GAAGnE,GAAG,CAACD,UAAU,CAACO,EAAE,EAAE,cAAc,CAAC,CAAC;QAC1D4D,WAAW,CAACE,GAAG,CAACD,cAAc,EAAE;UAC9B,GAAGP,eAAe;UAClBrC,SAAS,EAAEpB,eAAe,CAAC,CAAC;UAC5BsB,SAAS,EAAEtB,eAAe,CAAC;QAC7B,CAAC,CAAC;;QAEF;QACA,IAAIuD,UAAU,IAAIR,WAAW,CAACS,aAAa,KAAK,MAAM,EAAE;UACtD,MAAMU,WAAW,GAAGrE,GAAG,CAACM,EAAE,EAAE,WAAW,EAAEoD,UAAU,CAAC;UACpD,MAAMhC,WAAW,GAAG,MAAMwC,WAAW,CAACI,GAAG,CAACD,WAAW,CAAC;UAEtD,IAAI3C,WAAW,CAACY,MAAM,CAAC,CAAC,EAAE;YACxB,MAAMiC,WAAW,GAAG7C,WAAW,CAACc,IAAI,CAAC,CAAC,CAACnB,SAAS,IAAI,CAAC;YACrD6C,WAAW,CAACpB,MAAM,CAACuB,WAAW,EAAE;cAC9BhD,SAAS,EAAEkD,WAAW,GAAGtB,SAAS,CAACc,KAAK;cACxCtC,SAAS,EAAEtB,eAAe,CAAC;YAC7B,CAAC,CAAC;UACJ;QACF;QAEA,OAAOgE,cAAc,CAACxC,EAAE;MAC1B,CAAC,CAAC;;MAEF;MACA,MAAMG,kBAAkB,CAACsB,gBAAgB,CAAC;MAE1C,OAAOa,aAAa;IACtB,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDC,QAAQ,CAAC,4BAA4B,CAAC;MACtC,MAAMD,KAAK;IACb,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,OAAO;IACLsC,eAAe;IACfvC,OAAO;IACPE;EACF,CAAC;AACH,CAAC;AAACH,EAAA,CAtJWD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}