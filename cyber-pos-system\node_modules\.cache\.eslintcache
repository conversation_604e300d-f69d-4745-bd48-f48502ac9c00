[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "16", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts": "17", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx": "18", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx": "19", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx": "20", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts": "21", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts": "22", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts": "23", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx": "24", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx": "25", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx": "26", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx": "27", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx": "28", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx": "29", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx": "30", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx": "31"}, {"size": 362, "mtime": 1751001516282, "results": "32", "hashOfConfig": "33"}, {"size": 550, "mtime": 1751002257124, "results": "34", "hashOfConfig": "33"}, {"size": 2402, "mtime": 1751003134738, "results": "35", "hashOfConfig": "33"}, {"size": 5912, "mtime": 1751002889593, "results": "36", "hashOfConfig": "33"}, {"size": 6144, "mtime": 1751003013744, "results": "37", "hashOfConfig": "33"}, {"size": 5937, "mtime": 1751001851404, "results": "38", "hashOfConfig": "33"}, {"size": 8045, "mtime": 1751003816943, "results": "39", "hashOfConfig": "33"}, {"size": 1487, "mtime": 1751001889627, "results": "40", "hashOfConfig": "33"}, {"size": 7092, "mtime": 1751003059724, "results": "41", "hashOfConfig": "33"}, {"size": 16613, "mtime": 1751004208648, "results": "42", "hashOfConfig": "33"}, {"size": 6483, "mtime": 1751001825178, "results": "43", "hashOfConfig": "33"}, {"size": 13480, "mtime": 1751003649429, "results": "44", "hashOfConfig": "33"}, {"size": 665, "mtime": 1751001779724, "results": "45", "hashOfConfig": "33"}, {"size": 2499, "mtime": 1751003112125, "results": "46", "hashOfConfig": "33"}, {"size": 7270, "mtime": 1751002967950, "results": "47", "hashOfConfig": "33"}, {"size": 12716, "mtime": 1751002934527, "results": "48", "hashOfConfig": "33"}, {"size": 4406, "mtime": 1751003274192, "results": "49", "hashOfConfig": "33"}, {"size": 9054, "mtime": 1751003411814, "results": "50", "hashOfConfig": "33"}, {"size": 3964, "mtime": 1751003442458, "results": "51", "hashOfConfig": "33"}, {"size": 8498, "mtime": 1751003577995, "results": "52", "hashOfConfig": "33"}, {"size": 5162, "mtime": 1751003518513, "results": "53", "hashOfConfig": "33"}, {"size": 6079, "mtime": 1751003779872, "results": "54", "hashOfConfig": "33"}, {"size": 5107, "mtime": 1751003753867, "results": "55", "hashOfConfig": "33"}, {"size": 12775, "mtime": 1751004057085, "results": "56", "hashOfConfig": "33"}, {"size": 13401, "mtime": 1751003915007, "results": "57", "hashOfConfig": "33"}, {"size": 10910, "mtime": 1751003957303, "results": "58", "hashOfConfig": "33"}, {"size": 11914, "mtime": 1751004034012, "results": "59", "hashOfConfig": "33"}, {"size": 12558, "mtime": 1751004256417, "results": "60", "hashOfConfig": "33"}, {"size": 11536, "mtime": 1751004300206, "results": "61", "hashOfConfig": "33"}, {"size": 10775, "mtime": 1751004342324, "results": "62", "hashOfConfig": "33"}, {"size": 12479, "mtime": 1751004391960, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["157"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["158"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", ["159", "160", "161", "162", "163", "164", "165", "166"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", ["167", "168", "169", "170", "171"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", ["172", "173", "174", "175"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["176", "177"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["178", "179", "180"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["181", "182"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts", ["183", "184"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx", ["185"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts", ["186", "187"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts", ["188", "189"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx", ["190", "191", "192"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx", ["193"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx", ["194"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx", [], [], {"ruleId": "195", "severity": 1, "message": "196", "line": 17, "column": 7, "nodeType": "197", "messageId": "198", "endLine": 17, "endColumn": 62}, {"ruleId": "195", "severity": 1, "message": "199", "line": 16, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 16, "endColumn": 8}, {"ruleId": "195", "severity": 1, "message": "200", "line": 10, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 10, "endColumn": 7}, {"ruleId": "195", "severity": 1, "message": "201", "line": 11, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 11, "endColumn": 13}, {"ruleId": "195", "severity": 1, "message": "202", "line": 12, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 12, "endColumn": 10}, {"ruleId": "195", "severity": 1, "message": "203", "line": 13, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 13, "endColumn": 9}, {"ruleId": "195", "severity": 1, "message": "204", "line": 14, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 14, "endColumn": 7}, {"ruleId": "195", "severity": 1, "message": "205", "line": 15, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 15, "endColumn": 8}, {"ruleId": "195", "severity": 1, "message": "206", "line": 21, "column": 10, "nodeType": "197", "messageId": "198", "endLine": 21, "endColumn": 17}, {"ruleId": "195", "severity": 1, "message": "207", "line": 21, "column": 19, "nodeType": "197", "messageId": "198", "endLine": 21, "endColumn": 26}, {"ruleId": "195", "severity": 1, "message": "208", "line": 6, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 6, "endColumn": 9}, {"ruleId": "195", "severity": 1, "message": "209", "line": 9, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 9, "endColumn": 15}, {"ruleId": "195", "severity": 1, "message": "210", "line": 10, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 10, "endColumn": 12}, {"ruleId": "195", "severity": 1, "message": "211", "line": 14, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 14, "endColumn": 11}, {"ruleId": "195", "severity": 1, "message": "212", "line": 15, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 15, "endColumn": 9}, {"ruleId": "195", "severity": 1, "message": "213", "line": 9, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 9, "endColumn": 13}, {"ruleId": "195", "severity": 1, "message": "214", "line": 10, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 10, "endColumn": 6}, {"ruleId": "195", "severity": 1, "message": "215", "line": 14, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 14, "endColumn": 4}, {"ruleId": "195", "severity": 1, "message": "216", "line": 15, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 15, "endColumn": 7}, {"ruleId": "195", "severity": 1, "message": "217", "line": 7, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 7, "endColumn": 27}, {"ruleId": "195", "severity": 1, "message": "218", "line": 10, "column": 22, "nodeType": "197", "messageId": "198", "endLine": 10, "endColumn": 44}, {"ruleId": "195", "severity": 1, "message": "200", "line": 13, "column": 10, "nodeType": "197", "messageId": "198", "endLine": 13, "endColumn": 14}, {"ruleId": "195", "severity": 1, "message": "206", "line": 13, "column": 16, "nodeType": "197", "messageId": "198", "endLine": 13, "endColumn": 23}, {"ruleId": "195", "severity": 1, "message": "207", "line": 13, "column": 25, "nodeType": "197", "messageId": "198", "endLine": 13, "endColumn": 32}, {"ruleId": "195", "severity": 1, "message": "203", "line": 8, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 8, "endColumn": 9}, {"ruleId": "219", "severity": 1, "message": "220", "line": 34, "column": 6, "nodeType": "221", "endLine": 34, "endColumn": 8, "suggestions": "222"}, {"ruleId": "195", "severity": 1, "message": "223", "line": 5, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 5, "endColumn": 10}, {"ruleId": "195", "severity": 1, "message": "199", "line": 11, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 11, "endColumn": 8}, {"ruleId": "195", "severity": 1, "message": "214", "line": 2, "column": 25, "nodeType": "197", "messageId": "198", "endLine": 2, "endColumn": 28}, {"ruleId": "195", "severity": 1, "message": "223", "line": 5, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 5, "endColumn": 10}, {"ruleId": "195", "severity": 1, "message": "199", "line": 11, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 11, "endColumn": 8}, {"ruleId": "195", "severity": 1, "message": "224", "line": 3, "column": 10, "nodeType": "197", "messageId": "198", "endLine": 3, "endColumn": 31}, {"ruleId": "219", "severity": 1, "message": "225", "line": 87, "column": 6, "nodeType": "221", "endLine": 87, "endColumn": 8, "suggestions": "226"}, {"ruleId": "195", "severity": 1, "message": "213", "line": 8, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 8, "endColumn": 13}, {"ruleId": "195", "severity": 1, "message": "200", "line": 10, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 10, "endColumn": 7}, {"ruleId": "195", "severity": 1, "message": "201", "line": 11, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 11, "endColumn": 13}, {"ruleId": "195", "severity": 1, "message": "213", "line": 5, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 5, "endColumn": 13}, {"ruleId": "195", "severity": 1, "message": "227", "line": 9, "column": 3, "nodeType": "197", "messageId": "198", "endLine": 9, "endColumn": 10}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'User' is defined but never used.", "'CreditCard' is defined but never used.", "'Receipt' is defined but never used.", "'Trash2' is defined but never used.", "'Plus' is defined but never used.", "'Minus' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "'Filter' is defined but never used.", "'TrendingDown' is defined but never used.", "'BarChart3' is defined but never used.", "'Download' is defined but never used.", "'Upload' is defined but never used.", "'DollarSign' is defined but never used.", "'Tag' is defined but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'connectFirestoreEmulator' is defined but never used.", "'connectStorageEmulator' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["228"], "'getDocs' is defined but never used.", "'calculateServicePrice' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeFromCart'. Either include it or remove the dependency array.", ["229"], "'Printer' is defined but never used.", {"desc": "230", "fix": "231"}, {"desc": "232", "fix": "233"}, "Update the dependencies array to be: [loadUsers]", {"range": "234", "text": "235"}, "Update the dependencies array to be: [removeFromCart]", {"range": "236", "text": "237"}, [865, 867], "[loadUsers]", [2571, 2573], "[remove<PERSON><PERSON><PERSON><PERSON>]"]