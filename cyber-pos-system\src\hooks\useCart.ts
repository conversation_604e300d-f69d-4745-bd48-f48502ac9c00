import { useState, useCallback } from 'react';
import { CartItem, Service, Product } from '../types';
import { calculateServicePrice } from '../utils/serviceUtils';

export interface CartState {
  items: CartItem[];
  subtotal: number;
  discount: number;
  total: number;
  bundledValue: number;
}

export const useCart = (allServices: Service[] = []) => {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [discount, setDiscount] = useState(0);

  // Calculate cart totals
  const calculateTotals = useCallback((items: CartItem[], discountAmount: number = 0): CartState => {
    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
    
    // Calculate bundled services value
    const bundledValue = items.reduce((sum, item) => {
      if (item.type === 'service') {
        const service = allServices.find(s => s.id === item.itemId);
        if (service?.bundledServices?.length) {
          const bundledServices = service.bundledServices
            .map(id => allServices.find(s => s.id === id))
            .filter(Boolean) as Service[];
          return sum + bundledServices.reduce((bundleSum, bundledService) => 
            bundleSum + bundledService.basePrice, 0
          );
        }
      }
      return sum;
    }, 0);

    const total = Math.max(0, subtotal - discountAmount);

    return {
      items,
      subtotal,
      discount: discountAmount,
      total,
      bundledValue
    };
  }, [allServices]);

  // Add item to cart
  const addToCart = useCallback((
    item: Service | Product,
    type: 'service' | 'product',
    quantity: number = 1,
    customPrice?: number,
    notes?: string
  ) => {
    const itemId = item.id;
    const unitPrice = customPrice ?? (type === 'service' ? (item as Service).basePrice : (item as Product).price);
    const totalPrice = unitPrice * quantity;

    const cartItem: CartItem = {
      id: `${type}-${itemId}-${Date.now()}`,
      type,
      itemId,
      name: item.name,
      quantity,
      unitPrice,
      originalPrice: type === 'service' ? (item as Service).basePrice : (item as Product).price,
      totalPrice,
      notes
    };

    setCartItems(prev => [...prev, cartItem]);
  }, []);

  // Update item quantity
  const updateQuantity = useCallback((cartItemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(cartItemId);
      return;
    }

    setCartItems(prev => prev.map(item => 
      item.id === cartItemId 
        ? { ...item, quantity, totalPrice: item.unitPrice * quantity }
        : item
    ));
  }, []);

  // Update item price
  const updatePrice = useCallback((cartItemId: string, newPrice: number) => {
    setCartItems(prev => prev.map(item => 
      item.id === cartItemId 
        ? { ...item, unitPrice: newPrice, totalPrice: newPrice * item.quantity }
        : item
    ));
  }, []);

  // Update item notes
  const updateNotes = useCallback((cartItemId: string, notes: string) => {
    setCartItems(prev => prev.map(item => 
      item.id === cartItemId 
        ? { ...item, notes }
        : item
    ));
  }, []);

  // Remove item from cart
  const removeFromCart = useCallback((cartItemId: string) => {
    setCartItems(prev => prev.filter(item => item.id !== cartItemId));
  }, []);

  // Clear entire cart
  const clearCart = useCallback(() => {
    setCartItems([]);
    setDiscount(0);
  }, []);

  // Apply discount
  const applyDiscount = useCallback((discountAmount: number) => {
    setDiscount(Math.max(0, discountAmount));
  }, []);

  // Get cart state
  const cartState = calculateTotals(cartItems, discount);

  // Check if item exists in cart
  const isInCart = useCallback((itemId: string, type: 'service' | 'product') => {
    return cartItems.some(item => item.itemId === itemId && item.type === type);
  }, [cartItems]);

  // Get item quantity in cart
  const getItemQuantity = useCallback((itemId: string, type: 'service' | 'product') => {
    const item = cartItems.find(item => item.itemId === itemId && item.type === type);
    return item?.quantity || 0;
  }, [cartItems]);

  // Get bundled services for cart items
  const getBundledServices = useCallback(() => {
    const bundledServices: Array<{ service: Service; fromService: Service }> = [];
    
    cartItems.forEach(item => {
      if (item.type === 'service') {
        const service = allServices.find(s => s.id === item.itemId);
        if (service?.bundledServices?.length) {
          service.bundledServices.forEach(bundledId => {
            const bundledService = allServices.find(s => s.id === bundledId);
            if (bundledService) {
              bundledServices.push({ service: bundledService, fromService: service });
            }
          });
        }
      }
    });

    return bundledServices;
  }, [cartItems, allServices]);

  return {
    cartState,
    addToCart,
    updateQuantity,
    updatePrice,
    updateNotes,
    removeFromCart,
    clearCart,
    applyDiscount,
    isInCart,
    getItemQuantity,
    getBundledServices,
    itemCount: cartItems.length,
    isEmpty: cartItems.length === 0
  };
};
