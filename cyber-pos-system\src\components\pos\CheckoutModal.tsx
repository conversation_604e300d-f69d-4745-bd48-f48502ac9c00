import React, { useState } from 'react';
import {
  X,
  User,
  CreditCard,
  DollarSign,
  Smartphone,
  Receipt,
  Printer,
  Check,
  AlertCircle
} from 'lucide-react';
import { CartState } from '../../hooks/useCart';
import { PaymentMethod } from '../../types';

interface CheckoutModalProps {
  cartState: CartState;
  onClose: () => void;
  onComplete: (paymentData: {
    paymentMethod: PaymentMethod;
    customerId?: string;
    customerName?: string;
    notes?: string;
  }) => void;
}

const CheckoutModal: React.FC<CheckoutModalProps> = ({
  cartState,
  onClose,
  onComplete
}) => {
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [notes, setNotes] = useState('');
  const [processing, setProcessing] = useState(false);
  const [mpesaCode, setMpesaCode] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setProcessing(true);

    try {
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      onComplete({
        paymentMethod,
        customerName: customerName || undefined,
        notes: notes || undefined,
      });
    } catch (error) {
      console.error('Checkout error:', error);
    } finally {
      setProcessing(false);
    }
  };

  const paymentMethods = [
    {
      id: 'cash' as PaymentMethod,
      name: 'Cash',
      icon: DollarSign,
      description: 'Cash payment'
    },
    {
      id: 'mpesa' as PaymentMethod,
      name: 'M-PESA',
      icon: Smartphone,
      description: 'Mobile money payment'
    },
    {
      id: 'debt' as PaymentMethod,
      name: 'Credit/Debt',
      icon: User,
      description: 'Customer credit account'
    }
  ];

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Receipt className="h-5 w-5 mr-2" />
              Checkout
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Order Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-3">Order Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Items ({cartState.items.length}):</span>
                  <span>KSh {cartState.subtotal.toLocaleString()}</span>
                </div>
                {cartState.bundledValue > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Free Bundled Services:</span>
                    <span>KSh {cartState.bundledValue.toLocaleString()}</span>
                  </div>
                )}
                {cartState.discount > 0 && (
                  <div className="flex justify-between text-orange-600">
                    <span>Discount:</span>
                    <span>-KSh {cartState.discount.toLocaleString()}</span>
                  </div>
                )}
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>KSh {cartState.total.toLocaleString()}</span>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 flex items-center">
                <User className="h-4 w-4 mr-2" />
                Customer Information (Optional)
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Customer Name
                  </label>
                  <input
                    type="text"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter customer name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={customerPhone}
                    onChange={(e) => setCustomerPhone(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    placeholder="0700000000"
                  />
                </div>
              </div>
            </div>

            {/* Payment Method */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 flex items-center">
                <CreditCard className="h-4 w-4 mr-2" />
                Payment Method
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {paymentMethods.map((method) => (
                  <label
                    key={method.id}
                    className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                      paymentMethod === method.id
                        ? 'border-primary-600 ring-2 ring-primary-600'
                        : 'border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="payment-method"
                      value={method.id}
                      checked={paymentMethod === method.id}
                      onChange={(e) => setPaymentMethod(e.target.value as PaymentMethod)}
                      className="sr-only"
                    />
                    <div className="flex flex-col items-center text-center">
                      <method.icon className={`h-6 w-6 mb-2 ${
                        paymentMethod === method.id ? 'text-primary-600' : 'text-gray-400'
                      }`} />
                      <span className={`text-sm font-medium ${
                        paymentMethod === method.id ? 'text-primary-900' : 'text-gray-900'
                      }`}>
                        {method.name}
                      </span>
                      <span className="text-xs text-gray-500 mt-1">
                        {method.description}
                      </span>
                    </div>
                    {paymentMethod === method.id && (
                      <div className="absolute -inset-px rounded-lg border-2 border-primary-600 pointer-events-none" />
                    )}
                  </label>
                ))}
              </div>
            </div>

            {/* M-PESA Code Input */}
            {paymentMethod === 'mpesa' && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-2" />
                  <div className="flex-1">
                    <h5 className="text-sm font-medium text-blue-900 mb-2">
                      M-PESA Payment Instructions
                    </h5>
                    <p className="text-sm text-blue-700 mb-3">
                      1. Go to M-PESA menu on your phone<br />
                      2. Select "Lipa na M-PESA"<br />
                      3. Select "Buy Goods and Services"<br />
                      4. Enter Till Number: <strong>123456</strong><br />
                      5. Enter Amount: <strong>KSh {cartState.total.toLocaleString()}</strong><br />
                      6. Enter your M-PESA PIN and confirm
                    </p>
                    <div>
                      <label className="block text-sm font-medium text-blue-900 mb-1">
                        M-PESA Confirmation Code
                      </label>
                      <input
                        type="text"
                        value={mpesaCode}
                        onChange={(e) => setMpesaCode(e.target.value.toUpperCase())}
                        className="w-full border border-blue-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., QA12BC3456"
                        required={paymentMethod === 'mpesa'}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Credit/Debt Warning */}
            {paymentMethod === 'debt' && (
              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5 mr-2" />
                  <div>
                    <h5 className="text-sm font-medium text-orange-900 mb-1">
                      Credit Sale
                    </h5>
                    <p className="text-sm text-orange-700">
                      This transaction will be recorded as a debt. Customer name is required for credit sales.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Transaction Notes (Optional)
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Add any notes about this transaction..."
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={processing || (paymentMethod === 'debt' && !customerName.trim())}
                className="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {processing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Complete Transaction
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CheckoutModal;
