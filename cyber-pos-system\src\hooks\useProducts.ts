import { useState, useEffect } from 'react';
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Product } from '../types';

export const useProducts = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Real-time listener for products
  useEffect(() => {
    const productsQuery = query(
      collection(db, 'products'),
      orderBy('category'),
      orderBy('name')
    );

    const unsubscribe = onSnapshot(
      productsQuery,
      (snapshot) => {
        const productsData: Product[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          productsData.push({
            id: doc.id,
            name: data.name || '',
            description: data.description || '',
            price: data.price || 0,
            category: data.category || '',
            stockQuantity: data.stockQuantity || 0,
            reorderLevel: data.reorderLevel || 0,
            hasExpiry: data.hasExpiry || false,
            expiryDate: data.expiryDate?.toDate(),
            isActive: data.isActive !== false,
            createdAt: data.createdAt?.toDate() || new Date(),
            updatedAt: data.updatedAt?.toDate() || new Date(),
          });
        });
        setProducts(productsData);
        setLoading(false);
        setError(null);
      },
      (error) => {
        console.error('Error fetching products:', error);
        setError('Failed to fetch products');
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, []);

  const createProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setError(null);
      await addDoc(collection(db, 'products'), {
        ...productData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error creating product:', error);
      setError('Failed to create product');
      throw error;
    }
  };

  const updateProduct = async (productId: string, updates: Partial<Product>) => {
    try {
      setError(null);
      const { id, createdAt, ...updateData } = updates;
      await updateDoc(doc(db, 'products', productId), {
        ...updateData,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating product:', error);
      setError('Failed to update product');
      throw error;
    }
  };

  const deleteProduct = async (productId: string) => {
    try {
      setError(null);
      await deleteDoc(doc(db, 'products', productId));
    } catch (error) {
      console.error('Error deleting product:', error);
      setError('Failed to delete product');
      throw error;
    }
  };

  const updateStock = async (productId: string, newQuantity: number) => {
    try {
      setError(null);
      await updateDoc(doc(db, 'products', productId), {
        stockQuantity: newQuantity,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating stock:', error);
      setError('Failed to update stock');
      throw error;
    }
  };

  const getProductById = async (productId: string): Promise<Product | null> => {
    try {
      const productDoc = await getDoc(doc(db, 'products', productId));
      if (productDoc.exists()) {
        const data = productDoc.data();
        return {
          id: productDoc.id,
          name: data.name || '',
          description: data.description || '',
          price: data.price || 0,
          category: data.category || '',
          stockQuantity: data.stockQuantity || 0,
          reorderLevel: data.reorderLevel || 0,
          hasExpiry: data.hasExpiry || false,
          expiryDate: data.expiryDate?.toDate(),
          isActive: data.isActive !== false,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        };
      }
      return null;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw error;
    }
  };

  const getProductsByCategory = (category: string) => {
    return products.filter(product => product.category === category && product.isActive);
  };

  const getActiveProducts = () => {
    return products.filter(product => product.isActive);
  };

  const getInStockProducts = () => {
    return products.filter(product => product.isActive && product.stockQuantity > 0);
  };

  const getLowStockProducts = () => {
    return products.filter(product => 
      product.isActive && product.stockQuantity <= product.reorderLevel
    );
  };

  const getExpiringProducts = (daysAhead: number = 30) => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);
    
    return products.filter(product => 
      product.isActive && 
      product.hasExpiry && 
      product.expiryDate && 
      product.expiryDate <= futureDate
    );
  };

  const getProductCategories = () => {
    const categories = [...new Set(products.map(product => product.category))];
    return categories.sort();
  };

  const searchProducts = (searchTerm: string) => {
    if (!searchTerm.trim()) return getActiveProducts();
    
    const term = searchTerm.toLowerCase();
    return products.filter(product => 
      product.isActive && (
        product.name.toLowerCase().includes(term) ||
        product.description.toLowerCase().includes(term) ||
        product.category.toLowerCase().includes(term)
      )
    );
  };

  return {
    products,
    loading,
    error,
    createProduct,
    updateProduct,
    deleteProduct,
    updateStock,
    getProductById,
    getProductsByCategory,
    getActiveProducts,
    getInStockProducts,
    getLowStockProducts,
    getExpiringProducts,
    getProductCategories,
    searchProducts,
  };
};
