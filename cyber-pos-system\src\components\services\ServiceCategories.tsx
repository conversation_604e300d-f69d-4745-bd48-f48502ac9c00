import React from 'react';
import { Tag, Monitor, Printer, Globe, FileText, Building, Wrench } from 'lucide-react';

interface ServiceCategoriesProps {
  categories: string[];
  selectedCategory: string;
  onCategorySelect: (category: string) => void;
  serviceCounts: Record<string, number>;
}

const ServiceCategories: React.FC<ServiceCategoriesProps> = ({
  categories,
  selectedCategory,
  onCategorySelect,
  serviceCounts
}) => {
  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'printing':
        return <Printer className="h-4 w-4" />;
      case 'scanning':
        return <Monitor className="h-4 w-4" />;
      case 'internet':
        return <Globe className="h-4 w-4" />;
      case 'typing':
        return <FileText className="h-4 w-4" />;
      case 'government':
        return <Building className="h-4 w-4" />;
      case 'office':
        return <Wrench className="h-4 w-4" />;
      default:
        return <Tag className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'printing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'scanning':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'internet':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'typing':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'government':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'office':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
        <Tag className="h-5 w-5 mr-2" />
        Service Categories
      </h3>
      
      <div className="space-y-2">
        {/* All Categories */}
        <button
          onClick={() => onCategorySelect('')}
          className={`w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${
            selectedCategory === ''
              ? 'bg-primary-50 text-primary-700 border-primary-200'
              : 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100'
          }`}
        >
          <div className="flex items-center">
            <Tag className="h-4 w-4 mr-2" />
            <span className="font-medium">All Services</span>
          </div>
          <span className="text-sm bg-white px-2 py-1 rounded-full">
            {categories.reduce((total, cat) => total + (serviceCounts[cat] || 0), 0)}
          </span>
        </button>

        {/* Individual Categories */}
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => onCategorySelect(category)}
            className={`w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${
              selectedCategory === category
                ? 'bg-primary-50 text-primary-700 border-primary-200'
                : `${getCategoryColor(category)} hover:opacity-80`
            }`}
          >
            <div className="flex items-center">
              {getCategoryIcon(category)}
              <span className="ml-2 font-medium">{category}</span>
            </div>
            <span className="text-sm bg-white px-2 py-1 rounded-full">
              {serviceCounts[category] || 0}
            </span>
          </button>
        ))}
      </div>

      {categories.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Tag className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No categories yet</p>
          <p className="text-xs">Categories will appear when you add services</p>
        </div>
      )}
    </div>
  );
};

export default ServiceCategories;
