import React, { useState } from 'react';
import {
  Monitor,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  DollarSign,
  Tag,
  ToggleLeft,
  ToggleRight,
  Link,
  X,
  Save
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useServices } from '../../hooks/useServices';
import { Service } from '../../types';
import ServiceModal from './ServiceModal';
import ServiceCategories from './ServiceCategories';
import ServiceStats from './ServiceStats';

const Services: React.FC = () => {
  const { hasPermission } = useAuth();
  const {
    services,
    loading,
    error,
    createService,
    updateService,
    deleteService,
    getServiceCategories
  } = useServices();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [activeView, setActiveView] = useState<'grid' | 'stats'>('grid');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    basePrice: 0,
    category: '',
    allowPriceOverride: true,
    bundledServices: [] as string[],
    isActive: true
  });

  // Filter services based on search and category
  const filteredServices = services.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || service.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = getServiceCategories();

  // Calculate service counts per category
  const serviceCounts = categories.reduce((acc, category) => {
    acc[category] = services.filter(service => service.category === category).length;
    return acc;
  }, {} as Record<string, number>);

  const handleCreateService = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createService(formData);
      setShowCreateModal(false);
      resetForm();
    } catch (error) {
      console.error('Error creating service:', error);
    }
  };

  const handleUpdateService = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingService) return;

    try {
      await updateService(editingService.id, formData);
      setEditingService(null);
      resetForm();
    } catch (error) {
      console.error('Error updating service:', error);
    }
  };

  const handleDeleteService = async (serviceId: string) => {
    if (window.confirm('Are you sure you want to delete this service?')) {
      try {
        await deleteService(serviceId);
      } catch (error) {
        console.error('Error deleting service:', error);
      }
    }
  };

  const handleToggleActive = async (service: Service) => {
    try {
      await updateService(service.id, { isActive: !service.isActive });
    } catch (error) {
      console.error('Error toggling service status:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      basePrice: 0,
      category: '',
      allowPriceOverride: true,
      bundledServices: [],
      isActive: true
    });
  };

  const openEditModal = (service: Service) => {
    setFormData({
      name: service.name,
      description: service.description,
      basePrice: service.basePrice,
      category: service.category,
      allowPriceOverride: service.allowPriceOverride,
      bundledServices: service.bundledServices || [],
      isActive: service.isActive
    });
    setEditingService(service);
    setShowCreateModal(true);
  };

  if (!hasPermission('admin')) {
    return (
      <div className="text-center py-12">
        <Monitor className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
        <p className="mt-1 text-sm text-gray-500">
          You don't have permission to manage services.
        </p>
      </div>
    );
  }

  return (
    <div className="flex gap-6">
      {/* Sidebar - Categories */}
      <div className="w-64 flex-shrink-0">
        <ServiceCategories
          categories={categories}
          selectedCategory={selectedCategory}
          onCategorySelect={setSelectedCategory}
          serviceCounts={serviceCounts}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Monitor className="h-6 w-6 text-primary-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-900">Services Management</h1>
          </div>
          <div className="flex items-center space-x-3">
            {/* View Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveView('grid')}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  activeView === 'grid'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Services
              </button>
              <button
                onClick={() => setActiveView('stats')}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  activeView === 'stats'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Statistics
              </button>
            </div>

            <button
              onClick={() => {
                resetForm();
                setEditingService(null);
                setShowCreateModal(true);
              }}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Service
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* Search and Filter - Only show for grid view */}
        {activeView === 'grid' && (
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
          </div>
        )}

        {/* Content based on active view */}
        {activeView === 'stats' ? (
          <ServiceStats services={services} />
        ) : (
          <>
            {/* Services Grid */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading services...</p>
          </div>
        ) : filteredServices.length === 0 ? (
          <div className="text-center py-12">
            <Monitor className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No services found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || selectedCategory ? 'Try adjusting your search or filter.' : 'Get started by creating your first service.'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredServices.map((service) => (
              <ServiceCard
                key={service.id}
                service={service}
                onEdit={openEditModal}
                onDelete={handleDeleteService}
                onToggleActive={handleToggleActive}
                allServices={services}
              />
            ))}
          </div>
        )}
          </>
        )}
      </div>

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <ServiceModal
          service={editingService}
          formData={formData}
          setFormData={setFormData}
          onSubmit={editingService ? handleUpdateService : handleCreateService}
          onClose={() => {
            setShowCreateModal(false);
            setEditingService(null);
            resetForm();
          }}
          allServices={services}
        />
      )}
      </div>
    </div>
  );
};

// Service Card Component
interface ServiceCardProps {
  service: Service;
  onEdit: (service: Service) => void;
  onDelete: (serviceId: string) => void;
  onToggleActive: (service: Service) => void;
  allServices: Service[];
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  onEdit,
  onDelete,
  onToggleActive,
  allServices
}) => {
  const bundledServiceNames = service.bundledServices
    ?.map(id => allServices.find(s => s.id === id)?.name)
    .filter(Boolean) || [];

  return (
    <div className={`bg-white border rounded-lg p-4 ${service.isActive ? 'border-gray-200' : 'border-gray-300 bg-gray-50'}`}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h3 className={`font-semibold ${service.isActive ? 'text-gray-900' : 'text-gray-500'}`}>
            {service.name}
          </h3>
          <p className={`text-sm ${service.isActive ? 'text-gray-600' : 'text-gray-400'}`}>
            {service.description}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onEdit(service)}
            className="text-blue-600 hover:text-blue-800"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button
            onClick={() => onDelete(service.id)}
            className="text-red-600 hover:text-red-800"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500">Category:</span>
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
            {service.category}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500">Base Price:</span>
          <span className="font-semibold text-green-600">
            KSh {service.basePrice.toLocaleString()}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500">Price Override:</span>
          <span className={`text-xs ${service.allowPriceOverride ? 'text-green-600' : 'text-red-600'}`}>
            {service.allowPriceOverride ? 'Allowed' : 'Not Allowed'}
          </span>
        </div>

        {bundledServiceNames.length > 0 && (
          <div className="mt-2">
            <span className="text-sm text-gray-500">Bundled Services:</span>
            <div className="mt-1 flex flex-wrap gap-1">
              {bundledServiceNames.map((name, index) => (
                <span key={index} className="inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                  <Link className="h-3 w-3 mr-1" />
                  {name}
                </span>
              ))}
            </div>
          </div>
        )}

        <div className="flex items-center justify-between pt-2 border-t">
          <span className="text-sm text-gray-500">Status:</span>
          <button
            onClick={() => onToggleActive(service)}
            className="flex items-center"
          >
            {service.isActive ? (
              <ToggleRight className="h-5 w-5 text-green-500" />
            ) : (
              <ToggleLeft className="h-5 w-5 text-gray-400" />
            )}
            <span className={`ml-1 text-sm ${service.isActive ? 'text-green-600' : 'text-gray-500'}`}>
              {service.isActive ? 'Active' : 'Inactive'}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Services;
