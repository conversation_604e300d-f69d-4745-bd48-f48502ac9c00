{"ast": null, "code": "import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore, enableNetwork, disableNetwork } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\n// Firebase configuration\n// TODO: Replace with your actual Firebase config\nconst firebaseConfig = {\n  apiKey: \"your-api-key\",\n  authDomain: \"your-project.firebaseapp.com\",\n  projectId: \"your-project-id\",\n  storageBucket: \"your-project.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"your-app-id\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\n\n// Enable offline persistence\nexport const enableOfflineSupport = async () => {\n  try {\n    await disableNetwork(db);\n    await enableNetwork(db);\n    console.log('Firebase offline support enabled');\n  } catch (error) {\n    console.error('Error enabling offline support:', error);\n  }\n};\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "getFirestore", "enableNetwork", "disableNetwork", "getStorage", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "auth", "db", "storage", "enableOfflineSupport", "console", "log", "error"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/config/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth, connectAuthEmulator } from 'firebase/auth';\nimport {\n  getFirestore,\n  enableNetwork,\n  disableNetwork,\n  connectFirestoreEmulator,\n  enableIndexedDbPersistence\n} from 'firebase/firestore';\nimport { getStorage, connectStorageEmulator } from 'firebase/storage';\n\n// Firebase configuration\n// TODO: Replace with your actual Firebase config\nconst firebaseConfig = {\n  apiKey: \"your-api-key\",\n  authDomain: \"your-project.firebaseapp.com\",\n  projectId: \"your-project-id\",\n  storageBucket: \"your-project.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"your-app-id\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\n\n// Enable offline persistence\nexport const enableOfflineSupport = async () => {\n  try {\n    await disableNetwork(db);\n    await enableNetwork(db);\n    console.log('Firebase offline support enabled');\n  } catch (error) {\n    console.error('Error enabling offline support:', error);\n  }\n};\n\nexport default app;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,QAA6B,eAAe;AAC5D,SACEC,YAAY,EACZC,aAAa,EACbC,cAAc,QAGT,oBAAoB;AAC3B,SAASC,UAAU,QAAgC,kBAAkB;;AAErE;AACA;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,cAAc;EACtBC,UAAU,EAAE,8BAA8B;EAC1CC,SAAS,EAAE,iBAAiB;EAC5BC,aAAa,EAAE,0BAA0B;EACzCC,iBAAiB,EAAE,WAAW;EAC9BC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,GAAG,GAAGb,aAAa,CAACM,cAAc,CAAC;;AAEzC;AACA,OAAO,MAAMQ,IAAI,GAAGb,OAAO,CAACY,GAAG,CAAC;AAChC,OAAO,MAAME,EAAE,GAAGb,YAAY,CAACW,GAAG,CAAC;AACnC,OAAO,MAAMG,OAAO,GAAGX,UAAU,CAACQ,GAAG,CAAC;;AAEtC;AACA,OAAO,MAAMI,oBAAoB,GAAG,MAAAA,CAAA,KAAY;EAC9C,IAAI;IACF,MAAMb,cAAc,CAACW,EAAE,CAAC;IACxB,MAAMZ,aAAa,CAACY,EAAE,CAAC;IACvBG,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;EACzD;AACF,CAAC;AAED,eAAeP,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}