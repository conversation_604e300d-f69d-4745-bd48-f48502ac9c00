import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import {
  getFirestore,
  enableNetwork,
  disableNetwork,
  connectFirestoreEmulator,
  enableIndexedDbPersistence
} from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Firebase configuration
// For demo purposes, using Firebase emulator
// TODO: Replace with your actual Firebase config for production
const firebaseConfig = {
  apiKey: "demo-api-key",
  authDomain: "demo-project.firebaseapp.com",
  projectId: "demo-cyber-pos",
  storageBucket: "demo-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "demo-app-id"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Connect to emulators in development
if (process.env.NODE_ENV === 'development') {
  try {
    // Only connect if not already connected
    // Check if emulator is already connected by trying to connect
    connectAuthEmulator(auth, 'http://localhost:9099');
    // Note: Firestore emulator connection would go here if using emulator
    // connectFirestoreEmulator(db, 'localhost', 8080);
  } catch (error) {
    console.log('Emulators not available, using production Firebase');
  }
}

// Enable offline persistence
export const enableOfflineSupport = async () => {
  try {
    // Enable IndexedDB persistence for offline support
    await enableIndexedDbPersistence(db, {
      forceOwnership: false
    });
    console.log('Firebase offline persistence enabled');
  } catch (error: any) {
    if (error.code === 'failed-precondition') {
      console.warn('Multiple tabs open, persistence can only be enabled in one tab at a time');
    } else if (error.code === 'unimplemented') {
      console.warn('The current browser does not support offline persistence');
    } else {
      console.error('Error enabling offline persistence:', error);
    }
  }
};

// Network status management
export const goOffline = async () => {
  try {
    await disableNetwork(db);
    console.log('Firebase network disabled');
  } catch (error) {
    console.error('Error disabling network:', error);
  }
};

export const goOnline = async () => {
  try {
    await enableNetwork(db);
    console.log('Firebase network enabled');
  } catch (error) {
    console.error('Error enabling network:', error);
  }
};

export default app;
