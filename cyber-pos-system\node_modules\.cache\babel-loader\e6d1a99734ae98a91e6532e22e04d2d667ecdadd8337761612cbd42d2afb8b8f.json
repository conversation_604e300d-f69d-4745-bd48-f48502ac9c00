{"ast": null, "code": "import { initializeApp } from 'firebase/app';\nimport { getAuth, connectAuthEmulator } from 'firebase/auth';\nimport { getFirestore, enableNetwork, disableNetwork, enableIndexedDbPersistence } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\n// Firebase configuration\n// For demo purposes, using Firebase emulator\n// TODO: Replace with your actual Firebase config for production\nconst firebaseConfig = {\n  apiKey: \"demo-api-key\",\n  authDomain: \"demo-project.firebaseapp.com\",\n  projectId: \"demo-cyber-pos\",\n  storageBucket: \"demo-project.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"demo-app-id\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\n\n// Connect to emulators in development\nif (process.env.NODE_ENV === 'development') {\n  try {\n    // Only connect if not already connected\n    // Check if emulator is already connected by trying to connect\n    connectAuthEmulator(auth, 'http://localhost:9099');\n    // Note: Firestore emulator connection would go here if using emulator\n    // connectFirestoreEmulator(db, 'localhost', 8080);\n  } catch (error) {\n    console.log('Emulators not available, using production Firebase');\n  }\n}\n\n// Enable offline persistence\nexport const enableOfflineSupport = async () => {\n  try {\n    // Enable IndexedDB persistence for offline support\n    await enableIndexedDbPersistence(db, {\n      forceOwnership: false\n    });\n    console.log('Firebase offline persistence enabled');\n  } catch (error) {\n    if (error.code === 'failed-precondition') {\n      console.warn('Multiple tabs open, persistence can only be enabled in one tab at a time');\n    } else if (error.code === 'unimplemented') {\n      console.warn('The current browser does not support offline persistence');\n    } else {\n      console.error('Error enabling offline persistence:', error);\n    }\n  }\n};\n\n// Network status management\nexport const goOffline = async () => {\n  try {\n    await disableNetwork(db);\n    console.log('Firebase network disabled');\n  } catch (error) {\n    console.error('Error disabling network:', error);\n  }\n};\nexport const goOnline = async () => {\n  try {\n    await enableNetwork(db);\n    console.log('Firebase network enabled');\n  } catch (error) {\n    console.error('Error enabling network:', error);\n  }\n};\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "connectAuthEmulator", "getFirestore", "enableNetwork", "disableNetwork", "enableIndexedDbPersistence", "getStorage", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "auth", "db", "storage", "process", "env", "NODE_ENV", "error", "console", "log", "enableOfflineSupport", "forceOwnership", "code", "warn", "goOffline", "goOnline"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/config/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth, connectAuthEmulator } from 'firebase/auth';\nimport {\n  getFirestore,\n  enableNetwork,\n  disableNetwork,\n  connectFirestoreEmulator,\n  enableIndexedDbPersistence\n} from 'firebase/firestore';\nimport { getStorage, connectStorageEmulator } from 'firebase/storage';\n\n// Firebase configuration\n// For demo purposes, using Firebase emulator\n// TODO: Replace with your actual Firebase config for production\nconst firebaseConfig = {\n  apiKey: \"demo-api-key\",\n  authDomain: \"demo-project.firebaseapp.com\",\n  projectId: \"demo-cyber-pos\",\n  storageBucket: \"demo-project.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"demo-app-id\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\n\n// Connect to emulators in development\nif (process.env.NODE_ENV === 'development') {\n  try {\n    // Only connect if not already connected\n    // Check if emulator is already connected by trying to connect\n    connectAuthEmulator(auth, 'http://localhost:9099');\n    // Note: Firestore emulator connection would go here if using emulator\n    // connectFirestoreEmulator(db, 'localhost', 8080);\n  } catch (error) {\n    console.log('Emulators not available, using production Firebase');\n  }\n}\n\n// Enable offline persistence\nexport const enableOfflineSupport = async () => {\n  try {\n    // Enable IndexedDB persistence for offline support\n    await enableIndexedDbPersistence(db, {\n      forceOwnership: false\n    });\n    console.log('Firebase offline persistence enabled');\n  } catch (error: any) {\n    if (error.code === 'failed-precondition') {\n      console.warn('Multiple tabs open, persistence can only be enabled in one tab at a time');\n    } else if (error.code === 'unimplemented') {\n      console.warn('The current browser does not support offline persistence');\n    } else {\n      console.error('Error enabling offline persistence:', error);\n    }\n  }\n};\n\n// Network status management\nexport const goOffline = async () => {\n  try {\n    await disableNetwork(db);\n    console.log('Firebase network disabled');\n  } catch (error) {\n    console.error('Error disabling network:', error);\n  }\n};\n\nexport const goOnline = async () => {\n  try {\n    await enableNetwork(db);\n    console.log('Firebase network enabled');\n  } catch (error) {\n    console.error('Error enabling network:', error);\n  }\n};\n\nexport default app;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,EAAEC,mBAAmB,QAAQ,eAAe;AAC5D,SACEC,YAAY,EACZC,aAAa,EACbC,cAAc,EAEdC,0BAA0B,QACrB,oBAAoB;AAC3B,SAASC,UAAU,QAAgC,kBAAkB;;AAErE;AACA;AACA;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,cAAc;EACtBC,UAAU,EAAE,8BAA8B;EAC1CC,SAAS,EAAE,gBAAgB;EAC3BC,aAAa,EAAE,0BAA0B;EACzCC,iBAAiB,EAAE,WAAW;EAC9BC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,GAAG,GAAGf,aAAa,CAACQ,cAAc,CAAC;;AAEzC;AACA,OAAO,MAAMQ,IAAI,GAAGf,OAAO,CAACc,GAAG,CAAC;AAChC,OAAO,MAAME,EAAE,GAAGd,YAAY,CAACY,GAAG,CAAC;AACnC,OAAO,MAAMG,OAAO,GAAGX,UAAU,CAACQ,GAAG,CAAC;;AAEtC;AACA,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAC1C,IAAI;IACF;IACA;IACAnB,mBAAmB,CAACc,IAAI,EAAE,uBAAuB,CAAC;IAClD;IACA;EACF,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;EACnE;AACF;;AAEA;AACA,OAAO,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;EAC9C,IAAI;IACF;IACA,MAAMnB,0BAA0B,CAACW,EAAE,EAAE;MACnCS,cAAc,EAAE;IAClB,CAAC,CAAC;IACFH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;EACrD,CAAC,CAAC,OAAOF,KAAU,EAAE;IACnB,IAAIA,KAAK,CAACK,IAAI,KAAK,qBAAqB,EAAE;MACxCJ,OAAO,CAACK,IAAI,CAAC,0EAA0E,CAAC;IAC1F,CAAC,MAAM,IAAIN,KAAK,CAACK,IAAI,KAAK,eAAe,EAAE;MACzCJ,OAAO,CAACK,IAAI,CAAC,0DAA0D,CAAC;IAC1E,CAAC,MAAM;MACLL,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,SAAS,GAAG,MAAAA,CAAA,KAAY;EACnC,IAAI;IACF,MAAMxB,cAAc,CAACY,EAAE,CAAC;IACxBM,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;EAC1C,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;EAClD;AACF,CAAC;AAED,OAAO,MAAMQ,QAAQ,GAAG,MAAAA,CAAA,KAAY;EAClC,IAAI;IACF,MAAM1B,aAAa,CAACa,EAAE,CAAC;IACvBM,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;EACjD;AACF,CAAC;AAED,eAAeP,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}