{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\pos\\\\ProductSelector.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Plus, Package, AlertTriangle, Calendar, ShoppingCart, Minus } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductSelector = ({\n  products,\n  loading,\n  viewMode,\n  onAddToCart,\n  cart\n}) => {\n  _s();\n  const [quantities, setQuantities] = useState({});\n  const handleQuantityChange = (productId, quantity) => {\n    setQuantities(prev => ({\n      ...prev,\n      [productId]: Math.max(1, quantity)\n    }));\n  };\n  const handleAddToCart = product => {\n    const quantity = quantities[product.id] || 1;\n    onAddToCart(product, quantity);\n    // Reset quantity after adding\n    setQuantities(prev => ({\n      ...prev,\n      [product.id]: 1\n    }));\n  };\n  const getQuantity = productId => quantities[productId] || 1;\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2 text-gray-600\",\n        children: \"Loading products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this);\n  }\n  if (products.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(Package, {\n        className: \"mx-auto h-12 w-12 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"mt-2 text-sm font-medium text-gray-900\",\n        children: \"No products found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-500\",\n        children: \"Try adjusting your search or filter criteria, or check if products are in stock.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this);\n  }\n  if (viewMode === 'list') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: products.map(product => /*#__PURE__*/_jsxDEV(ProductListItem, {\n        product: product,\n        quantity: getQuantity(product.id),\n        onQuantityChange: quantity => handleQuantityChange(product.id, quantity),\n        onAddToCart: () => handleAddToCart(product),\n        isInCart: cart.isInCart(product.id, 'product'),\n        cartQuantity: cart.getItemQuantity(product.id, 'product')\n      }, product.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n    children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n      product: product,\n      quantity: getQuantity(product.id),\n      onQuantityChange: quantity => handleQuantityChange(product.id, quantity),\n      onAddToCart: () => handleAddToCart(product),\n      isInCart: cart.isInCart(product.id, 'product'),\n      cartQuantity: cart.getItemQuantity(product.id, 'product')\n    }, product.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n\n// Product Card Component\n_s(ProductSelector, \"/1j7rNgJjW382DkSnhNu8gymXhY=\");\n_c = ProductSelector;\nconst ProductCard = ({\n  product,\n  quantity,\n  onQuantityChange,\n  onAddToCart,\n  isInCart,\n  cartQuantity\n}) => {\n  const isLowStock = product.stockQuantity <= product.reorderLevel;\n  const isExpiringSoon = product.hasExpiry && product.expiryDate && product.expiryDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold text-gray-900\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600 mt-1\",\n          children: product.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 mt-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\",\n            children: product.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), isLowStock && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800\",\n            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n              className: \"h-3 w-3 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this), \"Low Stock\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), isInCart && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {\n          className: \"h-3 w-3 mr-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this), cartQuantity]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 space-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-600\",\n          children: \"In Stock:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `font-medium ${isLowStock ? 'text-red-600' : 'text-green-600'}`,\n          children: [product.stockQuantity, \" units\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), product.hasExpiry && product.expiryDate && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-600\",\n          children: \"Expires:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `font-medium ${isExpiringSoon ? 'text-orange-600' : 'text-gray-900'}`,\n          children: [product.expiryDate.toLocaleDateString(), isExpiringSoon && /*#__PURE__*/_jsxDEV(Calendar, {\n            className: \"h-3 w-3 inline ml-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Price:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-semibold text-green-600\",\n          children: [\"KSh \", product.price.toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm text-gray-600 mb-2\",\n        children: \"Quantity:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onQuantityChange(quantity - 1),\n          disabled: quantity <= 1,\n          className: \"p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: /*#__PURE__*/_jsxDEV(Minus, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"w-12 text-center font-medium\",\n          children: quantity\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onQuantityChange(quantity + 1),\n          disabled: quantity >= product.stockQuantity,\n          className: \"p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: /*#__PURE__*/_jsxDEV(Plus, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 mt-1\",\n        children: [\"Total: KSh \", (product.price * quantity).toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onAddToCart,\n      disabled: quantity > product.stockQuantity,\n      className: \"w-full bg-primary-600 text-white py-2 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n      children: [/*#__PURE__*/_jsxDEV(Plus, {\n        className: \"h-4 w-4 mr-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), \"Add to Cart\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n};\n\n// Product List Item Component (for list view)\n_c2 = ProductCard;\nconst ProductListItem = ({\n  product,\n  quantity,\n  onQuantityChange,\n  onAddToCart,\n  isInCart,\n  cartQuantity\n}) => {\n  const isLowStock = product.stockQuantity <= product.reorderLevel;\n  const isExpiringSoon = product.hasExpiry && product.expiryDate && product.expiryDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white border border-gray-200 rounded-lg p-4 flex items-center space-x-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold text-gray-900\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\",\n          children: product.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), isLowStock && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800\",\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), \"Low Stock\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this), isInCart && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs\",\n          children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), cartQuantity]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600\",\n        children: product.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4 mt-2 text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${isLowStock ? 'text-red-600' : 'text-green-600'}`,\n          children: [\"Stock: \", product.stockQuantity]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), product.hasExpiry && product.expiryDate && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${isExpiringSoon ? 'text-orange-600' : 'text-gray-600'}`,\n          children: [\"Expires: \", product.expiryDate.toLocaleDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-right\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"font-semibold text-green-600\",\n          children: [\"KSh \", product.price.toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-500\",\n          children: \"per unit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onQuantityChange(quantity - 1),\n          disabled: quantity <= 1,\n          className: \"p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50\",\n          children: /*#__PURE__*/_jsxDEV(Minus, {\n            className: \"h-3 w-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"w-8 text-center text-sm font-medium\",\n          children: quantity\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onQuantityChange(quantity + 1),\n          disabled: quantity >= product.stockQuantity,\n          className: \"p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50\",\n          children: /*#__PURE__*/_jsxDEV(Plus, {\n            className: \"h-3 w-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onAddToCart,\n        disabled: quantity > product.stockQuantity,\n        className: \"bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-4 w-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), \"Add\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 235,\n    columnNumber: 5\n  }, this);\n};\n_c3 = ProductListItem;\nexport default ProductSelector;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ProductSelector\");\n$RefreshReg$(_c2, \"ProductCard\");\n$RefreshReg$(_c3, \"ProductListItem\");", "map": {"version": 3, "names": ["React", "useState", "Plus", "Package", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Calendar", "ShoppingCart", "Minus", "jsxDEV", "_jsxDEV", "ProductSelector", "products", "loading", "viewMode", "onAddToCart", "cart", "_s", "quantities", "setQuantities", "handleQuantityChange", "productId", "quantity", "prev", "Math", "max", "handleAddToCart", "product", "id", "getQuantity", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "ProductListItem", "onQuantityChange", "isInCart", "cartQuantity", "getItemQuantity", "ProductCard", "_c", "isLowStock", "stockQuantity", "reorderLevel", "isExpiringSoon", "hasEx<PERSON>ry", "expiryDate", "Date", "now", "name", "description", "category", "toLocaleDateString", "price", "toLocaleString", "onClick", "disabled", "_c2", "_c3", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/pos/ProductSelector.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Plus,\n  Package,\n  AlertTriangle,\n  Calendar,\n  ShoppingCart,\n  Minus\n} from 'lucide-react';\nimport { Product } from '../../types';\n\ninterface ProductSelectorProps {\n  products: Product[];\n  loading: boolean;\n  viewMode: 'grid' | 'list';\n  onAddToCart: (product: Product, quantity: number) => void;\n  cart: {\n    isInCart: (itemId: string, type: 'service' | 'product') => boolean;\n    getItemQuantity: (itemId: string, type: 'service' | 'product') => number;\n  };\n}\n\nconst ProductSelector: React.FC<ProductSelectorProps> = ({\n  products,\n  loading,\n  viewMode,\n  onAddToCart,\n  cart\n}) => {\n  const [quantities, setQuantities] = useState<Record<string, number>>({});\n\n  const handleQuantityChange = (productId: string, quantity: number) => {\n    setQuantities(prev => ({\n      ...prev,\n      [productId]: Math.max(1, quantity)\n    }));\n  };\n\n  const handleAddToCart = (product: Product) => {\n    const quantity = quantities[product.id] || 1;\n    onAddToCart(product, quantity);\n    // Reset quantity after adding\n    setQuantities(prev => ({ ...prev, [product.id]: 1 }));\n  };\n\n  const getQuantity = (productId: string) => quantities[productId] || 1;\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n        <span className=\"ml-2 text-gray-600\">Loading products...</span>\n      </div>\n    );\n  }\n\n  if (products.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <Package className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No products found</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          Try adjusting your search or filter criteria, or check if products are in stock.\n        </p>\n      </div>\n    );\n  }\n\n  if (viewMode === 'list') {\n    return (\n      <div className=\"space-y-2\">\n        {products.map((product) => (\n          <ProductListItem\n            key={product.id}\n            product={product}\n            quantity={getQuantity(product.id)}\n            onQuantityChange={(quantity) => handleQuantityChange(product.id, quantity)}\n            onAddToCart={() => handleAddToCart(product)}\n            isInCart={cart.isInCart(product.id, 'product')}\n            cartQuantity={cart.getItemQuantity(product.id, 'product')}\n          />\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n      {products.map((product) => (\n        <ProductCard\n          key={product.id}\n          product={product}\n          quantity={getQuantity(product.id)}\n          onQuantityChange={(quantity) => handleQuantityChange(product.id, quantity)}\n          onAddToCart={() => handleAddToCart(product)}\n          isInCart={cart.isInCart(product.id, 'product')}\n          cartQuantity={cart.getItemQuantity(product.id, 'product')}\n        />\n      ))}\n    </div>\n  );\n};\n\n// Product Card Component\ninterface ProductItemProps {\n  product: Product;\n  quantity: number;\n  onQuantityChange: (quantity: number) => void;\n  onAddToCart: () => void;\n  isInCart: boolean;\n  cartQuantity: number;\n}\n\nconst ProductCard: React.FC<ProductItemProps> = ({\n  product,\n  quantity,\n  onQuantityChange,\n  onAddToCart,\n  isInCart,\n  cartQuantity\n}) => {\n  const isLowStock = product.stockQuantity <= product.reorderLevel;\n  const isExpiringSoon = product.hasExpiry && product.expiryDate && \n    product.expiryDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days\n\n  return (\n    <div className=\"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex-1\">\n          <h3 className=\"font-semibold text-gray-900\">{product.name}</h3>\n          <p className=\"text-sm text-gray-600 mt-1\">{product.description}</p>\n          <div className=\"flex items-center space-x-2 mt-2\">\n            <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\n              {product.category}\n            </span>\n            {isLowStock && (\n              <span className=\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800\">\n                <AlertTriangle className=\"h-3 w-3 mr-1\" />\n                Low Stock\n              </span>\n            )}\n          </div>\n        </div>\n        {isInCart && (\n          <div className=\"flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs\">\n            <ShoppingCart className=\"h-3 w-3 mr-1\" />\n            {cartQuantity}\n          </div>\n        )}\n      </div>\n\n      {/* Stock and Expiry Info */}\n      <div className=\"mb-3 space-y-1\">\n        <div className=\"flex justify-between text-sm\">\n          <span className=\"text-gray-600\">In Stock:</span>\n          <span className={`font-medium ${isLowStock ? 'text-red-600' : 'text-green-600'}`}>\n            {product.stockQuantity} units\n          </span>\n        </div>\n        {product.hasExpiry && product.expiryDate && (\n          <div className=\"flex justify-between text-sm\">\n            <span className=\"text-gray-600\">Expires:</span>\n            <span className={`font-medium ${isExpiringSoon ? 'text-orange-600' : 'text-gray-900'}`}>\n              {product.expiryDate.toLocaleDateString()}\n              {isExpiringSoon && (\n                <Calendar className=\"h-3 w-3 inline ml-1\" />\n              )}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Price */}\n      <div className=\"mb-4\">\n        <div className=\"flex justify-between items-center\">\n          <span className=\"text-sm text-gray-600\">Price:</span>\n          <span className=\"font-semibold text-green-600\">\n            KSh {product.price.toLocaleString()}\n          </span>\n        </div>\n      </div>\n\n      {/* Quantity Selector */}\n      <div className=\"mb-4\">\n        <label className=\"block text-sm text-gray-600 mb-2\">Quantity:</label>\n        <div className=\"flex items-center space-x-3\">\n          <button\n            onClick={() => onQuantityChange(quantity - 1)}\n            disabled={quantity <= 1}\n            className=\"p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Minus className=\"h-4 w-4\" />\n          </button>\n          <span className=\"w-12 text-center font-medium\">{quantity}</span>\n          <button\n            onClick={() => onQuantityChange(quantity + 1)}\n            disabled={quantity >= product.stockQuantity}\n            className=\"p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Plus className=\"h-4 w-4\" />\n          </button>\n        </div>\n        <div className=\"text-xs text-gray-500 mt-1\">\n          Total: KSh {(product.price * quantity).toLocaleString()}\n        </div>\n      </div>\n\n      {/* Add to Cart Button */}\n      <button\n        onClick={onAddToCart}\n        disabled={quantity > product.stockQuantity}\n        className=\"w-full bg-primary-600 text-white py-2 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\n      >\n        <Plus className=\"h-4 w-4 mr-2\" />\n        Add to Cart\n      </button>\n    </div>\n  );\n};\n\n// Product List Item Component (for list view)\nconst ProductListItem: React.FC<ProductItemProps> = ({\n  product,\n  quantity,\n  onQuantityChange,\n  onAddToCart,\n  isInCart,\n  cartQuantity\n}) => {\n  const isLowStock = product.stockQuantity <= product.reorderLevel;\n  const isExpiringSoon = product.hasExpiry && product.expiryDate && \n    product.expiryDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n\n  return (\n    <div className=\"bg-white border border-gray-200 rounded-lg p-4 flex items-center space-x-4\">\n      <div className=\"flex-1\">\n        <div className=\"flex items-center space-x-3 mb-2\">\n          <h3 className=\"font-semibold text-gray-900\">{product.name}</h3>\n          <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\n            {product.category}\n          </span>\n          {isLowStock && (\n            <span className=\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800\">\n              <AlertTriangle className=\"h-3 w-3 mr-1\" />\n              Low Stock\n            </span>\n          )}\n          {isInCart && (\n            <div className=\"flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs\">\n              <ShoppingCart className=\"h-3 w-3 mr-1\" />\n              {cartQuantity}\n            </div>\n          )}\n        </div>\n        <p className=\"text-sm text-gray-600\">{product.description}</p>\n        <div className=\"flex items-center space-x-4 mt-2 text-sm\">\n          <span className={`${isLowStock ? 'text-red-600' : 'text-green-600'}`}>\n            Stock: {product.stockQuantity}\n          </span>\n          {product.hasExpiry && product.expiryDate && (\n            <span className={`${isExpiringSoon ? 'text-orange-600' : 'text-gray-600'}`}>\n              Expires: {product.expiryDate.toLocaleDateString()}\n            </span>\n          )}\n        </div>\n      </div>\n\n      <div className=\"flex items-center space-x-4\">\n        {/* Price */}\n        <div className=\"text-right\">\n          <div className=\"font-semibold text-green-600\">\n            KSh {product.price.toLocaleString()}\n          </div>\n          <div className=\"text-xs text-gray-500\">\n            per unit\n          </div>\n        </div>\n\n        {/* Quantity Selector */}\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => onQuantityChange(quantity - 1)}\n            disabled={quantity <= 1}\n            className=\"p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50\"\n          >\n            <Minus className=\"h-3 w-3\" />\n          </button>\n          <span className=\"w-8 text-center text-sm font-medium\">{quantity}</span>\n          <button\n            onClick={() => onQuantityChange(quantity + 1)}\n            disabled={quantity >= product.stockQuantity}\n            className=\"p-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50\"\n          >\n            <Plus className=\"h-3 w-3\" />\n          </button>\n        </div>\n\n        {/* Add to Cart Button */}\n        <button\n          onClick={onAddToCart}\n          disabled={quantity > product.stockQuantity}\n          className=\"bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50 flex items-center\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductSelector;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,YAAY,EACZC,KAAK,QACA,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AActB,MAAMC,eAA+C,GAAGA,CAAC;EACvDC,QAAQ;EACRC,OAAO;EACPC,QAAQ;EACRC,WAAW;EACXC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAExE,MAAMkB,oBAAoB,GAAGA,CAACC,SAAiB,EAAEC,QAAgB,KAAK;IACpEH,aAAa,CAACI,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACF,SAAS,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,QAAQ;IACnC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,eAAe,GAAIC,OAAgB,IAAK;IAC5C,MAAML,QAAQ,GAAGJ,UAAU,CAACS,OAAO,CAACC,EAAE,CAAC,IAAI,CAAC;IAC5Cb,WAAW,CAACY,OAAO,EAAEL,QAAQ,CAAC;IAC9B;IACAH,aAAa,CAACI,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACI,OAAO,CAACC,EAAE,GAAG;IAAE,CAAC,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,WAAW,GAAIR,SAAiB,IAAKH,UAAU,CAACG,SAAS,CAAC,IAAI,CAAC;EAErE,IAAIR,OAAO,EAAE;IACX,oBACEH,OAAA;MAAKoB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDrB,OAAA;QAAKoB,SAAS,EAAC;MAAiE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvFzB,OAAA;QAAMoB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAEV;EAEA,IAAIvB,QAAQ,CAACwB,MAAM,KAAK,CAAC,EAAE;IACzB,oBACE1B,OAAA;MAAKoB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCrB,OAAA,CAACN,OAAO;QAAC0B,SAAS,EAAC;MAAiC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvDzB,OAAA;QAAIoB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7EzB,OAAA;QAAGoB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV;EAEA,IAAIrB,QAAQ,KAAK,MAAM,EAAE;IACvB,oBACEJ,OAAA;MAAKoB,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBnB,QAAQ,CAACyB,GAAG,CAAEV,OAAO,iBACpBjB,OAAA,CAAC4B,eAAe;QAEdX,OAAO,EAAEA,OAAQ;QACjBL,QAAQ,EAAEO,WAAW,CAACF,OAAO,CAACC,EAAE,CAAE;QAClCW,gBAAgB,EAAGjB,QAAQ,IAAKF,oBAAoB,CAACO,OAAO,CAACC,EAAE,EAAEN,QAAQ,CAAE;QAC3EP,WAAW,EAAEA,CAAA,KAAMW,eAAe,CAACC,OAAO,CAAE;QAC5Ca,QAAQ,EAAExB,IAAI,CAACwB,QAAQ,CAACb,OAAO,CAACC,EAAE,EAAE,SAAS,CAAE;QAC/Ca,YAAY,EAAEzB,IAAI,CAAC0B,eAAe,CAACf,OAAO,CAACC,EAAE,EAAE,SAAS;MAAE,GANrDD,OAAO,CAACC,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOhB,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,oBACEzB,OAAA;IAAKoB,SAAS,EAAC,sDAAsD;IAAAC,QAAA,EAClEnB,QAAQ,CAACyB,GAAG,CAAEV,OAAO,iBACpBjB,OAAA,CAACiC,WAAW;MAEVhB,OAAO,EAAEA,OAAQ;MACjBL,QAAQ,EAAEO,WAAW,CAACF,OAAO,CAACC,EAAE,CAAE;MAClCW,gBAAgB,EAAGjB,QAAQ,IAAKF,oBAAoB,CAACO,OAAO,CAACC,EAAE,EAAEN,QAAQ,CAAE;MAC3EP,WAAW,EAAEA,CAAA,KAAMW,eAAe,CAACC,OAAO,CAAE;MAC5Ca,QAAQ,EAAExB,IAAI,CAACwB,QAAQ,CAACb,OAAO,CAACC,EAAE,EAAE,SAAS,CAAE;MAC/Ca,YAAY,EAAEzB,IAAI,CAAC0B,eAAe,CAACf,OAAO,CAACC,EAAE,EAAE,SAAS;IAAE,GANrDD,OAAO,CAACC,EAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOhB,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAlB,EAAA,CAjFMN,eAA+C;AAAAiC,EAAA,GAA/CjC,eAA+C;AA2FrD,MAAMgC,WAAuC,GAAGA,CAAC;EAC/ChB,OAAO;EACPL,QAAQ;EACRiB,gBAAgB;EAChBxB,WAAW;EACXyB,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMI,UAAU,GAAGlB,OAAO,CAACmB,aAAa,IAAInB,OAAO,CAACoB,YAAY;EAChE,MAAMC,cAAc,GAAGrB,OAAO,CAACsB,SAAS,IAAItB,OAAO,CAACuB,UAAU,IAC5DvB,OAAO,CAACuB,UAAU,IAAI,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;EAEzE,oBACE1C,OAAA;IAAKoB,SAAS,EAAC,kFAAkF;IAAAC,QAAA,gBAC/FrB,OAAA;MAAKoB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDrB,OAAA;QAAKoB,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBrB,OAAA;UAAIoB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAEJ,OAAO,CAAC0B;QAAI;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/DzB,OAAA;UAAGoB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAEJ,OAAO,CAAC2B;QAAW;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnEzB,OAAA;UAAKoB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CrB,OAAA;YAAMoB,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACnGJ,OAAO,CAAC4B;UAAQ;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,EACNU,UAAU,iBACTnC,OAAA;YAAMoB,SAAS,EAAC,+FAA+F;YAAAC,QAAA,gBAC7GrB,OAAA,CAACL,aAAa;cAACyB,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAE5C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLK,QAAQ,iBACP9B,OAAA;QAAKoB,SAAS,EAAC,8EAA8E;QAAAC,QAAA,gBAC3FrB,OAAA,CAACH,YAAY;UAACuB,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACxCM,YAAY;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BrB,OAAA;QAAKoB,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CrB,OAAA;UAAMoB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChDzB,OAAA;UAAMoB,SAAS,EAAE,eAAee,UAAU,GAAG,cAAc,GAAG,gBAAgB,EAAG;UAAAd,QAAA,GAC9EJ,OAAO,CAACmB,aAAa,EAAC,QACzB;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EACLR,OAAO,CAACsB,SAAS,IAAItB,OAAO,CAACuB,UAAU,iBACtCxC,OAAA;QAAKoB,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CrB,OAAA;UAAMoB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/CzB,OAAA;UAAMoB,SAAS,EAAE,eAAekB,cAAc,GAAG,iBAAiB,GAAG,eAAe,EAAG;UAAAjB,QAAA,GACpFJ,OAAO,CAACuB,UAAU,CAACM,kBAAkB,CAAC,CAAC,EACvCR,cAAc,iBACbtC,OAAA,CAACJ,QAAQ;YAACwB,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBrB,OAAA;QAAKoB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDrB,OAAA;UAAMoB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrDzB,OAAA;UAAMoB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,GAAC,MACzC,EAACJ,OAAO,CAAC8B,KAAK,CAACC,cAAc,CAAC,CAAC;QAAA;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBrB,OAAA;QAAOoB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrEzB,OAAA;QAAKoB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CrB,OAAA;UACEiD,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACjB,QAAQ,GAAG,CAAC,CAAE;UAC9CsC,QAAQ,EAAEtC,QAAQ,IAAI,CAAE;UACxBQ,SAAS,EAAC,qGAAqG;UAAAC,QAAA,eAE/GrB,OAAA,CAACF,KAAK;YAACsB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACTzB,OAAA;UAAMoB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAET;QAAQ;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChEzB,OAAA;UACEiD,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACjB,QAAQ,GAAG,CAAC,CAAE;UAC9CsC,QAAQ,EAAEtC,QAAQ,IAAIK,OAAO,CAACmB,aAAc;UAC5ChB,SAAS,EAAC,qGAAqG;UAAAC,QAAA,eAE/GrB,OAAA,CAACP,IAAI;YAAC2B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNzB,OAAA;QAAKoB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,GAAC,aAC/B,EAAC,CAACJ,OAAO,CAAC8B,KAAK,GAAGnC,QAAQ,EAAEoC,cAAc,CAAC,CAAC;MAAA;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MACEiD,OAAO,EAAE5C,WAAY;MACrB6C,QAAQ,EAAEtC,QAAQ,GAAGK,OAAO,CAACmB,aAAc;MAC3ChB,SAAS,EAAC,oKAAoK;MAAAC,QAAA,gBAE9KrB,OAAA,CAACP,IAAI;QAAC2B,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEnC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAA0B,GAAA,GA3GMlB,WAAuC;AA4G7C,MAAML,eAA2C,GAAGA,CAAC;EACnDX,OAAO;EACPL,QAAQ;EACRiB,gBAAgB;EAChBxB,WAAW;EACXyB,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMI,UAAU,GAAGlB,OAAO,CAACmB,aAAa,IAAInB,OAAO,CAACoB,YAAY;EAChE,MAAMC,cAAc,GAAGrB,OAAO,CAACsB,SAAS,IAAItB,OAAO,CAACuB,UAAU,IAC5DvB,OAAO,CAACuB,UAAU,IAAI,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAEvE,oBACE1C,OAAA;IAAKoB,SAAS,EAAC,4EAA4E;IAAAC,QAAA,gBACzFrB,OAAA;MAAKoB,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACrBrB,OAAA;QAAKoB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CrB,OAAA;UAAIoB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAEJ,OAAO,CAAC0B;QAAI;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/DzB,OAAA;UAAMoB,SAAS,EAAC,sFAAsF;UAAAC,QAAA,EACnGJ,OAAO,CAAC4B;QAAQ;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,EACNU,UAAU,iBACTnC,OAAA;UAAMoB,SAAS,EAAC,+FAA+F;UAAAC,QAAA,gBAC7GrB,OAAA,CAACL,aAAa;YAACyB,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACAK,QAAQ,iBACP9B,OAAA;UAAKoB,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAC3FrB,OAAA,CAACH,YAAY;YAACuB,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxCM,YAAY;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNzB,OAAA;QAAGoB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAEJ,OAAO,CAAC2B;MAAW;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9DzB,OAAA;QAAKoB,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvDrB,OAAA;UAAMoB,SAAS,EAAE,GAAGe,UAAU,GAAG,cAAc,GAAG,gBAAgB,EAAG;UAAAd,QAAA,GAAC,SAC7D,EAACJ,OAAO,CAACmB,aAAa;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EACNR,OAAO,CAACsB,SAAS,IAAItB,OAAO,CAACuB,UAAU,iBACtCxC,OAAA;UAAMoB,SAAS,EAAE,GAAGkB,cAAc,GAAG,iBAAiB,GAAG,eAAe,EAAG;UAAAjB,QAAA,GAAC,WACjE,EAACJ,OAAO,CAACuB,UAAU,CAACM,kBAAkB,CAAC,CAAC;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzB,OAAA;MAAKoB,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1CrB,OAAA;QAAKoB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBrB,OAAA;UAAKoB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,GAAC,MACxC,EAACJ,OAAO,CAAC8B,KAAK,CAACC,cAAc,CAAC,CAAC;QAAA;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAEvC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzB,OAAA;QAAKoB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CrB,OAAA;UACEiD,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACjB,QAAQ,GAAG,CAAC,CAAE;UAC9CsC,QAAQ,EAAEtC,QAAQ,IAAI,CAAE;UACxBQ,SAAS,EAAC,yEAAyE;UAAAC,QAAA,eAEnFrB,OAAA,CAACF,KAAK;YAACsB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACTzB,OAAA;UAAMoB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAET;QAAQ;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvEzB,OAAA;UACEiD,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACjB,QAAQ,GAAG,CAAC,CAAE;UAC9CsC,QAAQ,EAAEtC,QAAQ,IAAIK,OAAO,CAACmB,aAAc;UAC5ChB,SAAS,EAAC,yEAAyE;UAAAC,QAAA,eAEnFrB,OAAA,CAACP,IAAI;YAAC2B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNzB,OAAA;QACEiD,OAAO,EAAE5C,WAAY;QACrB6C,QAAQ,EAAEtC,QAAQ,GAAGK,OAAO,CAACmB,aAAc;QAC3ChB,SAAS,EAAC,uHAAuH;QAAAC,QAAA,gBAEjIrB,OAAA,CAACP,IAAI;UAAC2B,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,OAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC2B,GAAA,GAxFIxB,eAA2C;AA0FjD,eAAe3B,eAAe;AAAC,IAAAiC,EAAA,EAAAiB,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAnB,EAAA;AAAAmB,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}