import React, { useState } from 'react';
import {
  ShoppingCart,
  Search,
  Filter,
  Grid,
  List,
  Package,
  Monitor,
  User,
  CreditCard,
  Receipt,
  Trash2,
  Plus,
  Minus
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useServices } from '../../hooks/useServices';
import { useProducts } from '../../hooks/useProducts';
import { useCart } from '../../hooks/useCart';
import { Service, Product } from '../../types';
import POSCart from './POSCart';
import ServiceSelector from './ServiceSelector';
import ProductSelector from './ProductSelector';

const POS: React.FC = () => {
  const { hasPermission } = useAuth();
  const { services, loading: servicesLoading } = useServices();
  const { products, loading: productsLoading } = useProducts();
  const cart = useCart(services);

  const [activeTab, setActiveTab] = useState<'services' | 'products'>('services');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Filter items based on search and category
  const filteredServices = services.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || service.category === selectedCategory;
    return service.isActive && matchesSearch && matchesCategory;
  });

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || product.category === selectedCategory;
    return product.isActive && product.stockQuantity > 0 && matchesSearch && matchesCategory;
  });

  const serviceCategories = [...new Set(services.map(s => s.category))].sort();
  const productCategories = [...new Set(products.map(p => p.category))].sort();
  const currentCategories = activeTab === 'services' ? serviceCategories : productCategories;

  if (!hasPermission(['admin', 'attendant'])) {
    return (
      <div className="text-center py-12">
        <ShoppingCart className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
        <p className="mt-1 text-sm text-gray-500">
          You don't have permission to access the POS system.
        </p>
      </div>
    );
  }

  return (
    <div className="flex gap-6 h-screen">
      {/* Main Content - Product/Service Selection */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <ShoppingCart className="h-6 w-6 text-primary-600 mr-2" />
              <h1 className="text-2xl font-bold text-gray-900">Point of Sale</h1>
            </div>

            <div className="flex items-center space-x-4">
              {/* View Mode Toggle */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white shadow-sm' : ''}`}
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${viewMode === 'list' ? 'bg-white shadow-sm' : ''}`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>

              {/* Cart Summary */}
              <div className="flex items-center bg-primary-50 text-primary-700 px-3 py-2 rounded-lg">
                <ShoppingCart className="h-4 w-4 mr-2" />
                <span className="font-medium">{cart.itemCount} items</span>
                <span className="ml-2 font-bold">KSh {cart.cartState.total.toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-4">
            <button
              onClick={() => {
                setActiveTab('services');
                setSelectedCategory('');
                setSearchTerm('');
              }}
              className={`flex-1 flex items-center justify-center py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'services'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Monitor className="h-4 w-4 mr-2" />
              Services ({services.filter(s => s.isActive).length})
            </button>
            <button
              onClick={() => {
                setActiveTab('products');
                setSelectedCategory('');
                setSearchTerm('');
              }}
              className={`flex-1 flex items-center justify-center py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'products'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Package className="h-4 w-4 mr-2" />
              Products ({products.filter(p => p.isActive && p.stockQuantity > 0).length})
            </button>
          </div>

          {/* Search and Filter */}
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder={`Search ${activeTab}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">All Categories</option>
                {currentCategories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 bg-white shadow rounded-lg p-4 overflow-hidden">
          {activeTab === 'services' ? (
            <ServiceSelector
              services={filteredServices}
              loading={servicesLoading}
              viewMode={viewMode}
              onAddToCart={(service, customPrice, notes) =>
                cart.addToCart(service, 'service', 1, customPrice, notes)
              }
              cart={cart}
            />
          ) : (
            <ProductSelector
              products={filteredProducts}
              loading={productsLoading}
              viewMode={viewMode}
              onAddToCart={(product, quantity) =>
                cart.addToCart(product, 'product', quantity)
              }
              cart={cart}
            />
          )}
        </div>
      </div>

      {/* Sidebar - Cart */}
      <div className="w-96 flex-shrink-0">
        <POSCart cart={cart} />
      </div>
    </div>
  );
};

export default POS;
