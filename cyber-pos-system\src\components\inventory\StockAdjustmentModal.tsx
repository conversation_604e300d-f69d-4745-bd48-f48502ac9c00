import React, { useState } from 'react';
import { X, Package, Plus, Minus, RefreshCw, AlertTriangle } from 'lucide-react';
import { Product } from '../../types';

interface StockAdjustmentModalProps {
  product: Product;
  onClose: () => void;
  onAdjust: (productId: string, newQuantity: number) => Promise<void>;
}

const StockAdjustmentModal: React.FC<StockAdjustmentModalProps> = ({
  product,
  onClose,
  onAdjust
}) => {
  const [adjustmentType, setAdjustmentType] = useState<'set' | 'add' | 'subtract'>('set');
  const [adjustmentValue, setAdjustmentValue] = useState(0);
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const calculateNewQuantity = () => {
    switch (adjustmentType) {
      case 'set':
        return adjustmentValue;
      case 'add':
        return product.stockQuantity + adjustmentValue;
      case 'subtract':
        return Math.max(0, product.stockQuantity - adjustmentValue);
      default:
        return product.stockQuantity;
    }
  };

  const newQuantity = calculateNewQuantity();
  const difference = newQuantity - product.stockQuantity;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      if (newQuantity < 0) {
        throw new Error('Stock quantity cannot be negative');
      }

      await onAdjust(product.id, newQuantity);
      onClose();
    } catch (error: any) {
      setError(error.message || 'Failed to adjust stock');
    } finally {
      setLoading(false);
    }
  };

  const adjustmentTypes = [
    {
      id: 'set' as const,
      name: 'Set To',
      description: 'Set stock to exact amount',
      icon: RefreshCw
    },
    {
      id: 'add' as const,
      name: 'Add Stock',
      description: 'Add to current stock',
      icon: Plus
    },
    {
      id: 'subtract' as const,
      name: 'Remove Stock',
      description: 'Remove from current stock',
      icon: Minus
    }
  ];

  const commonReasons = [
    'Stock received',
    'Stock sold',
    'Stock damaged',
    'Stock expired',
    'Stock returned',
    'Inventory count correction',
    'Other'
  ];

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Adjust Stock
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Product Info */}
          <div className="bg-gray-50 p-4 rounded-lg mb-6">
            <h4 className="font-medium text-gray-900">{product.name}</h4>
            <p className="text-sm text-gray-600">{product.description}</p>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600">Current Stock:</span>
              <span className="font-semibold text-gray-900">{product.stockQuantity} units</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Reorder Level:</span>
              <span className="font-semibold text-gray-900">{product.reorderLevel} units</span>
            </div>
          </div>

          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Adjustment Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Adjustment Type
              </label>
              <div className="grid grid-cols-1 gap-3">
                {adjustmentTypes.map((type) => (
                  <label
                    key={type.id}
                    className={`relative flex cursor-pointer rounded-lg border p-3 focus:outline-none ${
                      adjustmentType === type.id
                        ? 'border-primary-600 ring-2 ring-primary-600'
                        : 'border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="adjustment-type"
                      value={type.id}
                      checked={adjustmentType === type.id}
                      onChange={(e) => setAdjustmentType(e.target.value as any)}
                      className="sr-only"
                    />
                    <div className="flex items-center">
                      <type.icon className={`h-5 w-5 mr-3 ${
                        adjustmentType === type.id ? 'text-primary-600' : 'text-gray-400'
                      }`} />
                      <div>
                        <span className={`text-sm font-medium ${
                          adjustmentType === type.id ? 'text-primary-900' : 'text-gray-900'
                        }`}>
                          {type.name}
                        </span>
                        <p className="text-xs text-gray-500">{type.description}</p>
                      </div>
                    </div>
                    {adjustmentType === type.id && (
                      <div className="absolute -inset-px rounded-lg border-2 border-primary-600 pointer-events-none" />
                    )}
                  </label>
                ))}
              </div>
            </div>

            {/* Adjustment Value */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {adjustmentType === 'set' ? 'New Stock Quantity' : 
                 adjustmentType === 'add' ? 'Quantity to Add' : 'Quantity to Remove'}
              </label>
              <input
                type="number"
                required
                min="0"
                value={adjustmentValue}
                onChange={(e) => setAdjustmentValue(parseInt(e.target.value) || 0)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Enter quantity"
              />
            </div>

            {/* Preview */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Preview</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-blue-700">Current Stock:</span>
                  <span className="font-medium text-blue-900">{product.stockQuantity} units</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">New Stock:</span>
                  <span className="font-medium text-blue-900">{newQuantity} units</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Change:</span>
                  <span className={`font-medium ${
                    difference > 0 ? 'text-green-600' : difference < 0 ? 'text-red-600' : 'text-blue-900'
                  }`}>
                    {difference > 0 ? '+' : ''}{difference} units
                  </span>
                </div>
              </div>
              
              {/* Warnings */}
              {newQuantity <= product.reorderLevel && newQuantity > 0 && (
                <div className="mt-3 flex items-start">
                  <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 mr-2" />
                  <span className="text-xs text-orange-700">
                    New stock level is below reorder threshold ({product.reorderLevel} units)
                  </span>
                </div>
              )}
              
              {newQuantity === 0 && (
                <div className="mt-3 flex items-start">
                  <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 mr-2" />
                  <span className="text-xs text-red-700">
                    This will mark the product as out of stock
                  </span>
                </div>
              )}
            </div>

            {/* Reason */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reason for Adjustment
              </label>
              <select
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500 mb-2"
              >
                <option value="">Select a reason</option>
                {commonReasons.map(reasonOption => (
                  <option key={reasonOption} value={reasonOption}>{reasonOption}</option>
                ))}
              </select>
              {reason === 'Other' && (
                <textarea
                  placeholder="Please specify the reason..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  rows={2}
                />
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || adjustmentValue < 0 || (adjustmentType === 'subtract' && adjustmentValue > product.stockQuantity)}
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Adjusting...' : 'Adjust Stock'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default StockAdjustmentModal;
