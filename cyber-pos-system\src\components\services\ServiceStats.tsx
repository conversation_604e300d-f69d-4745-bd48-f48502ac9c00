import React from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Activity,
  Tag,
  ToggleRight,
  ToggleLeft
} from 'lucide-react';
import { Service } from '../../types';
import { getServiceCategoriesWithCounts, formatPrice } from '../../utils/serviceUtils';

interface ServiceStatsProps {
  services: Service[];
}

const ServiceStats: React.FC<ServiceStatsProps> = ({ services }) => {
  const totalServices = services.length;
  const activeServices = services.filter(s => s.isActive).length;
  const inactiveServices = totalServices - activeServices;
  
  const averagePrice = services.length > 0 
    ? services.reduce((sum, service) => sum + service.basePrice, 0) / services.length 
    : 0;
  
  const highestPrice = services.length > 0 
    ? Math.max(...services.map(s => s.basePrice)) 
    : 0;
  
  const lowestPrice = services.length > 0 
    ? Math.min(...services.map(s => s.basePrice)) 
    : 0;
  
  const categoriesWithCounts = getServiceCategoriesWithCounts(services);
  const totalCategories = categoriesWithCounts.length;
  
  const servicesWithBundles = services.filter(s => s.bundledServices && s.bundledServices.length > 0).length;
  const servicesWithPriceOverride = services.filter(s => s.allowPriceOverride).length;

  const stats = [
    {
      name: 'Total Services',
      value: totalServices.toString(),
      icon: Activity,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      name: 'Active Services',
      value: activeServices.toString(),
      icon: ToggleRight,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      name: 'Categories',
      value: totalCategories.toString(),
      icon: Tag,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      name: 'Average Price',
      value: formatPrice(averagePrice),
      icon: DollarSign,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <div key={stat.name} className="bg-white rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-5 w-5 ${stat.color}`} />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <p className="text-lg font-semibold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Service Status Breakdown */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Service Status
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ToggleRight className="h-4 w-4 text-green-500 mr-2" />
                <span className="text-sm text-gray-600">Active Services</span>
              </div>
              <span className="text-sm font-semibold text-gray-900">{activeServices}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ToggleLeft className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-sm text-gray-600">Inactive Services</span>
              </div>
              <span className="text-sm font-semibold text-gray-900">{inactiveServices}</span>
            </div>
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Activation Rate</span>
                <span className="text-sm font-semibold text-green-600">
                  {totalServices > 0 ? Math.round((activeServices / totalServices) * 100) : 0}%
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Analysis */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Pricing Analysis
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Highest Price</span>
              <span className="text-sm font-semibold text-gray-900">{formatPrice(highestPrice)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Lowest Price</span>
              <span className="text-sm font-semibold text-gray-900">{formatPrice(lowestPrice)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Average Price</span>
              <span className="text-sm font-semibold text-gray-900">{formatPrice(averagePrice)}</span>
            </div>
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Price Override Allowed</span>
                <span className="text-sm font-semibold text-blue-600">{servicesWithPriceOverride}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Category Breakdown */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Tag className="h-5 w-5 mr-2" />
            Categories
          </h3>
          <div className="space-y-2">
            {categoriesWithCounts.length > 0 ? (
              categoriesWithCounts.map(({ category, count, activeCount }) => (
                <div key={category} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span className="text-sm font-medium text-gray-700">{category}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">{activeCount}/{count}</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${count > 0 ? (activeCount / count) * 100 : 0}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">No categories yet</p>
            )}
          </div>
        </div>

        {/* Service Features */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Service Features
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Services with Bundles</span>
              <span className="text-sm font-semibold text-gray-900">{servicesWithBundles}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Price Override Enabled</span>
              <span className="text-sm font-semibold text-gray-900">{servicesWithPriceOverride}</span>
            </div>
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Bundle Usage</span>
                <span className="text-sm font-semibold text-purple-600">
                  {totalServices > 0 ? Math.round((servicesWithBundles / totalServices) * 100) : 0}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceStats;
