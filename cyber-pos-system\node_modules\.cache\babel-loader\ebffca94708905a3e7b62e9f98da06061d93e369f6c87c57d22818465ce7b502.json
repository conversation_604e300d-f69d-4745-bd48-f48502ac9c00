{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\inventory\\\\Inventory.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Package, Plus, Search } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useProducts } from '../../hooks/useProducts';\nimport ProductModal from './ProductModal';\nimport InventoryStats from './InventoryStats';\nimport StockAdjustmentModal from './StockAdjustmentModal';\nimport LowStockAlert from './LowStockAlert';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Inventory = () => {\n  _s();\n  const {\n    hasPermission\n  } = useAuth();\n  const {\n    products,\n    loading,\n    error,\n    updateProduct,\n    deleteProduct,\n    updateStock,\n    getLowStockProducts,\n    getExpiringProducts,\n    getProductCategories\n  } = useProducts();\n  const [activeView, setActiveView] = useState('products');\n  const [showProductModal, setShowProductModal] = useState(false);\n  const [showStockModal, setShowStockModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [adjustingStock, setAdjustingStock] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [stockFilter, setStockFilter] = useState('all');\n\n  // Filter products based on search, category, and stock status\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !selectedCategory || product.category === selectedCategory;\n    let matchesStock = true;\n    switch (stockFilter) {\n      case 'low':\n        matchesStock = product.stockQuantity <= product.reorderLevel && product.stockQuantity > 0;\n        break;\n      case 'out':\n        matchesStock = product.stockQuantity === 0;\n        break;\n      case 'expiring':\n        const thirtyDaysFromNow = new Date();\n        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);\n        matchesStock = product.hasExpiry && product.expiryDate && product.expiryDate <= thirtyDaysFromNow;\n        break;\n    }\n    return matchesSearch && matchesCategory && matchesStock;\n  });\n  const categories = getProductCategories();\n  const lowStockProducts = getLowStockProducts();\n  const expiringProducts = getExpiringProducts();\n  const handleEditProduct = product => {\n    setEditingProduct(product);\n    setShowProductModal(true);\n  };\n  const handleDeleteProduct = async productId => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await deleteProduct(productId);\n      } catch (error) {\n        console.error('Error deleting product:', error);\n      }\n    }\n  };\n  const handleStockAdjustment = product => {\n    setAdjustingStock(product);\n    setShowStockModal(true);\n  };\n  const handleToggleActive = async product => {\n    try {\n      await updateProduct(product.id, {\n        isActive: !product.isActive\n      });\n    } catch (error) {\n      console.error('Error toggling product status:', error);\n    }\n  };\n  if (!hasPermission(['admin', 'attendant'])) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(Package, {\n        className: \"mx-auto h-12 w-12 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"mt-2 text-sm font-medium text-gray-900\",\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-500\",\n        children: \"You don't have permission to access inventory management.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Package, {\n            className: \"h-6 w-6 text-primary-600 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Inventory Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex bg-gray-100 rounded-lg p-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveView('products'),\n              className: `px-3 py-1 rounded text-sm font-medium transition-colors ${activeView === 'products' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n              children: \"Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveView('stats'),\n              className: `px-3 py-1 rounded text-sm font-medium transition-colors ${activeView === 'stats' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n              children: \"Statistics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveView('alerts'),\n              className: `px-3 py-1 rounded text-sm font-medium transition-colors ${activeView === 'alerts' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n              children: [\"Alerts\", (lowStockProducts.length > 0 || expiringProducts.length > 0) && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5\",\n                children: lowStockProducts.length + expiringProducts.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), hasPermission('admin') && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setEditingProduct(null);\n              setShowProductModal(true);\n            },\n            className: \"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), \"Add Product\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this), activeView === 'products' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 relative\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search products...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: e => setSelectedCategory(e.target.value),\n            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category,\n              children: category\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: stockFilter,\n            onChange: e => setStockFilter(e.target.value),\n            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"low\",\n              children: \"Low Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"out\",\n              children: \"Out of Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"expiring\",\n              children: \"Expiring Soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), activeView === 'stats' ? /*#__PURE__*/_jsxDEV(InventoryStats, {\n      products: products\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 9\n    }, this) : activeView === 'alerts' ? /*#__PURE__*/_jsxDEV(LowStockAlert, {\n      lowStockProducts: lowStockProducts,\n      expiringProducts: expiringProducts,\n      onStockAdjust: handleStockAdjustment\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-500\",\n            children: \"Loading products...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 15\n        }, this) : filteredProducts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(Package, {\n            className: \"mx-auto h-12 w-12 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"mt-2 text-sm font-medium text-gray-900\",\n            children: \"No products found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-500\",\n            children: searchTerm || selectedCategory || stockFilter !== 'all' ? 'Try adjusting your search or filter criteria.' : 'Get started by adding your first product.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n          children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product,\n            onEdit: handleEditProduct,\n            onDelete: handleDeleteProduct,\n            onStockAdjust: handleStockAdjustment,\n            onToggleActive: handleToggleActive,\n            canEdit: hasPermission('admin')\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this)\n    }, void 0, false), showProductModal && /*#__PURE__*/_jsxDEV(ProductModal, {\n      product: editingProduct,\n      onClose: () => {\n        setShowProductModal(false);\n        setEditingProduct(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 9\n    }, this), showStockModal && adjustingStock && /*#__PURE__*/_jsxDEV(StockAdjustmentModal, {\n      product: adjustingStock,\n      onClose: () => {\n        setShowStockModal(false);\n        setAdjustingStock(null);\n      },\n      onAdjust: updateStock\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(Inventory, \"AeqCXDFhK+CqE8Kg0lB8JmwXNM0=\", false, function () {\n  return [useAuth, useProducts];\n});\n_c = Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");", "map": {"version": 3, "names": ["React", "useState", "Package", "Plus", "Search", "useAuth", "useProducts", "ProductModal", "InventoryStats", "StockAdjustmentModal", "LowStockAlert", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Inventory", "_s", "hasPermission", "products", "loading", "error", "updateProduct", "deleteProduct", "updateStock", "getLowStockProducts", "getExpiringProducts", "getProductCategories", "activeView", "setActiveView", "showProductModal", "setShowProductModal", "showStockModal", "setShowStockModal", "editingProduct", "setEditingProduct", "adjustingStock", "setAdjustingStock", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "stockFilter", "setStockFilter", "filteredProducts", "filter", "product", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesCategory", "category", "matchesStock", "stockQuantity", "reorderLevel", "thirtyDaysFromNow", "Date", "setDate", "getDate", "hasEx<PERSON>ry", "expiryDate", "categories", "lowStockProducts", "expiringProducts", "handleEditProduct", "handleDeleteProduct", "productId", "window", "confirm", "console", "handleStockAdjustment", "handleToggleActive", "id", "isActive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "type", "placeholder", "value", "onChange", "e", "target", "map", "onStockAdjust", "ProductCard", "onEdit", "onDelete", "onToggleActive", "canEdit", "onClose", "onAdjust", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/inventory/Inventory.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Package,\n  Plus,\n  Search,\n  Filter,\n  AlertTriangle,\n  Calendar,\n  TrendingDown,\n  BarChart3,\n  Edit,\n  Trash2,\n  RefreshCw,\n  Download,\n  Upload\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useProducts } from '../../hooks/useProducts';\nimport { Product } from '../../types';\nimport ProductModal from './ProductModal';\nimport InventoryStats from './InventoryStats';\nimport StockAdjustmentModal from './StockAdjustmentModal';\nimport LowStockAlert from './LowStockAlert';\n\nconst Inventory: React.FC = () => {\n  const { hasPermission } = useAuth();\n  const {\n    products,\n    loading,\n    error,\n    updateProduct,\n    deleteProduct,\n    updateStock,\n    getLowStockProducts,\n    getExpiringProducts,\n    getProductCategories\n  } = useProducts();\n\n  const [activeView, setActiveView] = useState<'products' | 'stats' | 'alerts'>('products');\n  const [showProductModal, setShowProductModal] = useState(false);\n  const [showStockModal, setShowStockModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null);\n  const [adjustingStock, setAdjustingStock] = useState<Product | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [stockFilter, setStockFilter] = useState<'all' | 'low' | 'out' | 'expiring'>('all');\n\n  // Filter products based on search, category, and stock status\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !selectedCategory || product.category === selectedCategory;\n\n    let matchesStock = true;\n    switch (stockFilter) {\n      case 'low':\n        matchesStock = product.stockQuantity <= product.reorderLevel && product.stockQuantity > 0;\n        break;\n      case 'out':\n        matchesStock = product.stockQuantity === 0;\n        break;\n      case 'expiring':\n        const thirtyDaysFromNow = new Date();\n        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);\n        matchesStock = product.hasExpiry && product.expiryDate && product.expiryDate <= thirtyDaysFromNow;\n        break;\n    }\n\n    return matchesSearch && matchesCategory && matchesStock;\n  });\n\n  const categories = getProductCategories();\n  const lowStockProducts = getLowStockProducts();\n  const expiringProducts = getExpiringProducts();\n\n  const handleEditProduct = (product: Product) => {\n    setEditingProduct(product);\n    setShowProductModal(true);\n  };\n\n  const handleDeleteProduct = async (productId: string) => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await deleteProduct(productId);\n      } catch (error) {\n        console.error('Error deleting product:', error);\n      }\n    }\n  };\n\n  const handleStockAdjustment = (product: Product) => {\n    setAdjustingStock(product);\n    setShowStockModal(true);\n  };\n\n  const handleToggleActive = async (product: Product) => {\n    try {\n      await updateProduct(product.id, { isActive: !product.isActive });\n    } catch (error) {\n      console.error('Error toggling product status:', error);\n    }\n  };\n\n  if (!hasPermission(['admin', 'attendant'])) {\n    return (\n      <div className=\"text-center py-12\">\n        <Package className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Access Denied</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          You don't have permission to access inventory management.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center\">\n            <Package className=\"h-6 w-6 text-primary-600 mr-2\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">Inventory Management</h1>\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            {/* View Toggle */}\n            <div className=\"flex bg-gray-100 rounded-lg p-1\">\n              <button\n                onClick={() => setActiveView('products')}\n                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${\n                  activeView === 'products'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Products\n              </button>\n              <button\n                onClick={() => setActiveView('stats')}\n                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${\n                  activeView === 'stats'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Statistics\n              </button>\n              <button\n                onClick={() => setActiveView('alerts')}\n                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${\n                  activeView === 'alerts'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Alerts\n                {(lowStockProducts.length > 0 || expiringProducts.length > 0) && (\n                  <span className=\"ml-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5\">\n                    {lowStockProducts.length + expiringProducts.length}\n                  </span>\n                )}\n              </button>\n            </div>\n\n            {hasPermission('admin') && (\n              <button\n                onClick={() => {\n                  setEditingProduct(null);\n                  setShowProductModal(true);\n                }}\n                className=\"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Product\n              </button>\n            )}\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n            {error}\n          </div>\n        )}\n\n        {/* Search and Filters - Only show for products view */}\n        {activeView === 'products' && (\n          <div className=\"flex flex-col lg:flex-row gap-4 mb-6\">\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n              />\n            </div>\n\n            <div className=\"flex gap-3\">\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category} value={category}>{category}</option>\n                ))}\n              </select>\n\n              <select\n                value={stockFilter}\n                onChange={(e) => setStockFilter(e.target.value as any)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"all\">All Stock</option>\n                <option value=\"low\">Low Stock</option>\n                <option value=\"out\">Out of Stock</option>\n                <option value=\"expiring\">Expiring Soon</option>\n              </select>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Content based on active view */}\n      {activeView === 'stats' ? (\n        <InventoryStats products={products} />\n      ) : activeView === 'alerts' ? (\n        <LowStockAlert\n          lowStockProducts={lowStockProducts}\n          expiringProducts={expiringProducts}\n          onStockAdjust={handleStockAdjustment}\n        />\n      ) : (\n        <>\n          {/* Products Grid */}\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            {loading ? (\n              <div className=\"text-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"></div>\n                <p className=\"mt-2 text-gray-500\">Loading products...</p>\n              </div>\n            ) : filteredProducts.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <Package className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No products found</h3>\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  {searchTerm || selectedCategory || stockFilter !== 'all'\n                    ? 'Try adjusting your search or filter criteria.'\n                    : 'Get started by adding your first product.'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {filteredProducts.map((product) => (\n                  <ProductCard\n                    key={product.id}\n                    product={product}\n                    onEdit={handleEditProduct}\n                    onDelete={handleDeleteProduct}\n                    onStockAdjust={handleStockAdjustment}\n                    onToggleActive={handleToggleActive}\n                    canEdit={hasPermission('admin')}\n                  />\n                ))}\n              </div>\n            )}\n          </div>\n        </>\n      )}\n\n      {/* Modals */}\n      {showProductModal && (\n        <ProductModal\n          product={editingProduct}\n          onClose={() => {\n            setShowProductModal(false);\n            setEditingProduct(null);\n          }}\n        />\n      )}\n\n      {showStockModal && adjustingStock && (\n        <StockAdjustmentModal\n          product={adjustingStock}\n          onClose={() => {\n            setShowStockModal(false);\n            setAdjustingStock(null);\n          }}\n          onAdjust={updateStock}\n        />\n      )}\n    </div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,OAAO,EACPC,IAAI,EACJC,MAAM,QAWD,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,QAAQ,yBAAyB;AAErD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAc,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACnC,MAAM;IACJa,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,aAAa;IACbC,aAAa;IACbC,WAAW;IACXC,mBAAmB;IACnBC,mBAAmB;IACnBC;EACF,CAAC,GAAGpB,WAAW,CAAC,CAAC;EAEjB,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAkC,UAAU,CAAC;EACzF,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAqC,KAAK,CAAC;;EAEzF;EACA,MAAM0C,gBAAgB,GAAGzB,QAAQ,CAAC0B,MAAM,CAACC,OAAO,IAAI;IAClD,MAAMC,aAAa,GAAGD,OAAO,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC,IAC9DH,OAAO,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC;IACzF,MAAMG,eAAe,GAAG,CAACZ,gBAAgB,IAAIM,OAAO,CAACO,QAAQ,KAAKb,gBAAgB;IAElF,IAAIc,YAAY,GAAG,IAAI;IACvB,QAAQZ,WAAW;MACjB,KAAK,KAAK;QACRY,YAAY,GAAGR,OAAO,CAACS,aAAa,IAAIT,OAAO,CAACU,YAAY,IAAIV,OAAO,CAACS,aAAa,GAAG,CAAC;QACzF;MACF,KAAK,KAAK;QACRD,YAAY,GAAGR,OAAO,CAACS,aAAa,KAAK,CAAC;QAC1C;MACF,KAAK,UAAU;QACb,MAAME,iBAAiB,GAAG,IAAIC,IAAI,CAAC,CAAC;QACpCD,iBAAiB,CAACE,OAAO,CAACF,iBAAiB,CAACG,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;QAC3DN,YAAY,GAAGR,OAAO,CAACe,SAAS,IAAIf,OAAO,CAACgB,UAAU,IAAIhB,OAAO,CAACgB,UAAU,IAAIL,iBAAiB;QACjG;IACJ;IAEA,OAAOV,aAAa,IAAIK,eAAe,IAAIE,YAAY;EACzD,CAAC,CAAC;EAEF,MAAMS,UAAU,GAAGpC,oBAAoB,CAAC,CAAC;EACzC,MAAMqC,gBAAgB,GAAGvC,mBAAmB,CAAC,CAAC;EAC9C,MAAMwC,gBAAgB,GAAGvC,mBAAmB,CAAC,CAAC;EAE9C,MAAMwC,iBAAiB,GAAIpB,OAAgB,IAAK;IAC9CX,iBAAiB,CAACW,OAAO,CAAC;IAC1Bf,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMoC,mBAAmB,GAAG,MAAOC,SAAiB,IAAK;IACvD,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAM/C,aAAa,CAAC6C,SAAS,CAAC;MAChC,CAAC,CAAC,OAAO/C,KAAK,EAAE;QACdkD,OAAO,CAAClD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF;EACF,CAAC;EAED,MAAMmD,qBAAqB,GAAI1B,OAAgB,IAAK;IAClDT,iBAAiB,CAACS,OAAO,CAAC;IAC1Bb,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMwC,kBAAkB,GAAG,MAAO3B,OAAgB,IAAK;IACrD,IAAI;MACF,MAAMxB,aAAa,CAACwB,OAAO,CAAC4B,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC7B,OAAO,CAAC6B;MAAS,CAAC,CAAC;IAClE,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdkD,OAAO,CAAClD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,IAAI,CAACH,aAAa,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,EAAE;IAC1C,oBACEL,OAAA;MAAK+D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChE,OAAA,CAACV,OAAO;QAACyE,SAAS,EAAC;MAAiC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvDpE,OAAA;QAAI+D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzEpE,OAAA;QAAG+D,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV;EAEA,oBACEpE,OAAA;IAAK+D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBhE,OAAA;MAAK+D,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7ChE,OAAA;QAAK+D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDhE,OAAA;UAAK+D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChChE,OAAA,CAACV,OAAO;YAACyE,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDpE,OAAA;YAAI+D,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAENpE,OAAA;UAAK+D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAE1ChE,OAAA;YAAK+D,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9ChE,OAAA;cACEqE,OAAO,EAAEA,CAAA,KAAMrD,aAAa,CAAC,UAAU,CAAE;cACzC+C,SAAS,EAAE,2DACThD,UAAU,KAAK,UAAU,GACrB,kCAAkC,GAClC,mCAAmC,EACtC;cAAAiD,QAAA,EACJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpE,OAAA;cACEqE,OAAO,EAAEA,CAAA,KAAMrD,aAAa,CAAC,OAAO,CAAE;cACtC+C,SAAS,EAAE,2DACThD,UAAU,KAAK,OAAO,GAClB,kCAAkC,GAClC,mCAAmC,EACtC;cAAAiD,QAAA,EACJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpE,OAAA;cACEqE,OAAO,EAAEA,CAAA,KAAMrD,aAAa,CAAC,QAAQ,CAAE;cACvC+C,SAAS,EAAE,2DACThD,UAAU,KAAK,QAAQ,GACnB,kCAAkC,GAClC,mCAAmC,EACtC;cAAAiD,QAAA,GACJ,QAEC,EAAC,CAACb,gBAAgB,CAACmB,MAAM,GAAG,CAAC,IAAIlB,gBAAgB,CAACkB,MAAM,GAAG,CAAC,kBAC1DtE,OAAA;gBAAM+D,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC5Eb,gBAAgB,CAACmB,MAAM,GAAGlB,gBAAgB,CAACkB;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL/D,aAAa,CAAC,OAAO,CAAC,iBACrBL,OAAA;YACEqE,OAAO,EAAEA,CAAA,KAAM;cACb/C,iBAAiB,CAAC,IAAI,CAAC;cACvBJ,mBAAmB,CAAC,IAAI,CAAC;YAC3B,CAAE;YACF6C,SAAS,EAAC,uFAAuF;YAAAC,QAAA,gBAEjGhE,OAAA,CAACT,IAAI;cAACwE,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL5D,KAAK,iBACJR,OAAA;QAAK+D,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFxD;MAAK;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGArD,UAAU,KAAK,UAAU,iBACxBf,OAAA;QAAK+D,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDhE,OAAA;UAAK+D,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BhE,OAAA,CAACR,MAAM;YAACuE,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FpE,OAAA;YACEuE,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oBAAoB;YAChCC,KAAK,EAAEhD,UAAW;YAClBiD,QAAQ,EAAGC,CAAC,IAAKjD,aAAa,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CV,SAAS,EAAC;UAA0G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpE,OAAA;UAAK+D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhE,OAAA;YACEyE,KAAK,EAAE9C,gBAAiB;YACxB+C,QAAQ,EAAGC,CAAC,IAAK/C,mBAAmB,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACrDV,SAAS,EAAC,6FAA6F;YAAAC,QAAA,gBAEvGhE,OAAA;cAAQyE,KAAK,EAAC,EAAE;cAAAT,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACvClB,UAAU,CAAC2B,GAAG,CAACrC,QAAQ,iBACtBxC,OAAA;cAAuByE,KAAK,EAAEjC,QAAS;cAAAwB,QAAA,EAAExB;YAAQ,GAApCA,QAAQ;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqC,CAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAETpE,OAAA;YACEyE,KAAK,EAAE5C,WAAY;YACnB6C,QAAQ,EAAGC,CAAC,IAAK7C,cAAc,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAY,CAAE;YACvDV,SAAS,EAAC,6FAA6F;YAAAC,QAAA,gBAEvGhE,OAAA;cAAQyE,KAAK,EAAC,KAAK;cAAAT,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCpE,OAAA;cAAQyE,KAAK,EAAC,KAAK;cAAAT,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCpE,OAAA;cAAQyE,KAAK,EAAC,KAAK;cAAAT,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzCpE,OAAA;cAAQyE,KAAK,EAAC,UAAU;cAAAT,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLrD,UAAU,KAAK,OAAO,gBACrBf,OAAA,CAACJ,cAAc;MAACU,QAAQ,EAAEA;IAAS;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACpCrD,UAAU,KAAK,QAAQ,gBACzBf,OAAA,CAACF,aAAa;MACZqD,gBAAgB,EAAEA,gBAAiB;MACnCC,gBAAgB,EAAEA,gBAAiB;MACnC0B,aAAa,EAAEnB;IAAsB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,gBAEFpE,OAAA,CAAAE,SAAA;MAAA8D,QAAA,eAEEhE,OAAA;QAAK+D,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAC5CzD,OAAO,gBACNP,OAAA;UAAK+D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BhE,OAAA;YAAK+D,SAAS,EAAC;UAAyE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/FpE,OAAA;YAAG+D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,GACJrC,gBAAgB,CAACuC,MAAM,KAAK,CAAC,gBAC/BtE,OAAA;UAAK+D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChChE,OAAA,CAACV,OAAO;YAACyE,SAAS,EAAC;UAAiC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDpE,OAAA;YAAI+D,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EpE,OAAA;YAAG+D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACtCvC,UAAU,IAAIE,gBAAgB,IAAIE,WAAW,KAAK,KAAK,GACpD,+CAA+C,GAC/C;UAA2C;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAENpE,OAAA;UAAK+D,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEjC,gBAAgB,CAAC8C,GAAG,CAAE5C,OAAO,iBAC5BjC,OAAA,CAAC+E,WAAW;YAEV9C,OAAO,EAAEA,OAAQ;YACjB+C,MAAM,EAAE3B,iBAAkB;YAC1B4B,QAAQ,EAAE3B,mBAAoB;YAC9BwB,aAAa,EAAEnB,qBAAsB;YACrCuB,cAAc,EAAEtB,kBAAmB;YACnCuB,OAAO,EAAE9E,aAAa,CAAC,OAAO;UAAE,GAN3B4B,OAAO,CAAC4B,EAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOhB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC,gBACN,CACH,EAGAnD,gBAAgB,iBACfjB,OAAA,CAACL,YAAY;MACXsC,OAAO,EAAEZ,cAAe;MACxB+D,OAAO,EAAEA,CAAA,KAAM;QACblE,mBAAmB,CAAC,KAAK,CAAC;QAC1BI,iBAAiB,CAAC,IAAI,CAAC;MACzB;IAAE;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEAjD,cAAc,IAAII,cAAc,iBAC/BvB,OAAA,CAACH,oBAAoB;MACnBoC,OAAO,EAAEV,cAAe;MACxB6D,OAAO,EAAEA,CAAA,KAAM;QACbhE,iBAAiB,CAAC,KAAK,CAAC;QACxBI,iBAAiB,CAAC,IAAI,CAAC;MACzB,CAAE;MACF6D,QAAQ,EAAE1E;IAAY;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChE,EAAA,CAjRID,SAAmB;EAAA,QACGV,OAAO,EAW7BC,WAAW;AAAA;AAAA4F,EAAA,GAZXnF,SAAmB;AAAA,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}