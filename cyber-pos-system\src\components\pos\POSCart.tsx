import React, { useState } from 'react';
import {
  ShoppingCart,
  Trash2,
  Plus,
  Minus,
  Edit3,
  Receipt,
  Percent,
  Gift,
  X,
  Check
} from 'lucide-react';
import { CartState } from '../../hooks/useCart';
import { useAuth } from '../../contexts/AuthContext';
import { useTransactions } from '../../hooks/useTransactions';
import { printReceipt } from '../../utils/receiptGenerator';
import CheckoutModal from './CheckoutModal';

interface POSCartProps {
  cart: {
    cartState: CartState;
    updateQuantity: (cartItemId: string, quantity: number) => void;
    updatePrice: (cartItemId: string, newPrice: number) => void;
    updateNotes: (cartItemId: string, notes: string) => void;
    removeFromCart: (cartItemId: string) => void;
    clearCart: () => void;
    applyDiscount: (discountAmount: number) => void;
    getBundledServices: () => Array<{ service: any; fromService: any }>;
    isEmpty: boolean;
  };
}

const POSCart: React.FC<POSCartProps> = ({ cart }) => {
  const { currentUser } = useAuth();
  const { saveTransaction, loading: transactionLoading } = useTransactions();
  const [editingPrice, setEditingPrice] = useState<string | null>(null);
  const [editingNotes, setEditingNotes] = useState<string | null>(null);
  const [tempPrice, setTempPrice] = useState('');
  const [tempNotes, setTempNotes] = useState('');
  const [discountInput, setDiscountInput] = useState('');
  const [showCheckout, setShowCheckout] = useState(false);

  const { cartState } = cart;
  const bundledServices = cart.getBundledServices();

  const handlePriceEdit = (itemId: string, currentPrice: number) => {
    setEditingPrice(itemId);
    setTempPrice(currentPrice.toString());
  };

  const handlePriceSave = (itemId: string) => {
    const newPrice = parseFloat(tempPrice);
    if (!isNaN(newPrice) && newPrice >= 0) {
      cart.updatePrice(itemId, newPrice);
    }
    setEditingPrice(null);
    setTempPrice('');
  };

  const handleNotesEdit = (itemId: string, currentNotes: string = '') => {
    setEditingNotes(itemId);
    setTempNotes(currentNotes);
  };

  const handleNotesSave = (itemId: string) => {
    cart.updateNotes(itemId, tempNotes);
    setEditingNotes(null);
    setTempNotes('');
  };

  const handleDiscountApply = () => {
    const discount = parseFloat(discountInput);
    if (!isNaN(discount) && discount >= 0) {
      cart.applyDiscount(discount);
      setDiscountInput('');
    }
  };

  if (cart.isEmpty) {
    return (
      <div className="bg-white shadow rounded-lg p-6 h-full flex flex-col items-center justify-center">
        <ShoppingCart className="h-16 w-16 text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Cart is Empty</h3>
        <p className="text-sm text-gray-500 text-center">
          Add services or products to start a new transaction
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2" />
            Cart ({cartState.items.length})
          </h2>
          <button
            onClick={cart.clearCart}
            className="text-red-600 hover:text-red-800 p-1"
            title="Clear Cart"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Cart Items */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {cartState.items.map((item) => (
          <div key={item.id} className="border border-gray-200 rounded-lg p-3">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{item.name}</h4>
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  <span className={`px-2 py-1 rounded ${
                    item.type === 'service' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {item.type}
                  </span>
                  {item.unitPrice !== item.originalPrice && (
                    <span className="text-orange-600">Custom Price</span>
                  )}
                </div>
              </div>
              <button
                onClick={() => cart.removeFromCart(item.id)}
                className="text-red-600 hover:text-red-800 p-1"
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            {/* Quantity Controls */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => cart.updateQuantity(item.id, item.quantity - 1)}
                  className="p-1 rounded border border-gray-300 hover:bg-gray-50"
                >
                  <Minus className="h-3 w-3" />
                </button>
                <span className="w-8 text-center text-sm font-medium">{item.quantity}</span>
                <button
                  onClick={() => cart.updateQuantity(item.id, item.quantity + 1)}
                  className="p-1 rounded border border-gray-300 hover:bg-gray-50"
                >
                  <Plus className="h-3 w-3" />
                </button>
              </div>

              {/* Price */}
              <div className="flex items-center space-x-2">
                {editingPrice === item.id ? (
                  <div className="flex items-center space-x-1">
                    <input
                      type="number"
                      value={tempPrice}
                      onChange={(e) => setTempPrice(e.target.value)}
                      className="w-16 px-2 py-1 text-xs border border-gray-300 rounded"
                      step="0.01"
                      min="0"
                    />
                    <button
                      onClick={() => handlePriceSave(item.id)}
                      className="text-green-600 hover:text-green-800"
                    >
                      <Check className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => setEditingPrice(null)}
                      className="text-gray-600 hover:text-gray-800"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-1">
                    <span className="text-sm font-medium">KSh {item.unitPrice.toLocaleString()}</span>
                    <button
                      onClick={() => handlePriceEdit(item.id, item.unitPrice)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <Edit3 className="h-3 w-3" />
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            {editingNotes === item.id ? (
              <div className="flex items-center space-x-2 mt-2">
                <input
                  type="text"
                  value={tempNotes}
                  onChange={(e) => setTempNotes(e.target.value)}
                  placeholder="Add notes..."
                  className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded"
                />
                <button
                  onClick={() => handleNotesSave(item.id)}
                  className="text-green-600 hover:text-green-800"
                >
                  <Check className="h-3 w-3" />
                </button>
                <button
                  onClick={() => setEditingNotes(null)}
                  className="text-gray-600 hover:text-gray-800"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ) : (
              <div className="flex items-center justify-between mt-2">
                <div className="flex-1">
                  {item.notes ? (
                    <p className="text-xs text-gray-600 italic">"{item.notes}"</p>
                  ) : (
                    <button
                      onClick={() => handleNotesEdit(item.id, item.notes)}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      Add notes
                    </button>
                  )}
                </div>
                {item.notes && (
                  <button
                    onClick={() => handleNotesEdit(item.id, item.notes)}
                    className="text-blue-600 hover:text-blue-800 ml-2"
                  >
                    <Edit3 className="h-3 w-3" />
                  </button>
                )}
              </div>
            )}

            {/* Total for this item */}
            <div className="flex justify-between items-center mt-2 pt-2 border-t border-gray-100">
              <span className="text-xs text-gray-500">Total:</span>
              <span className="font-semibold text-gray-900">KSh {item.totalPrice.toLocaleString()}</span>
            </div>
          </div>
        ))}

        {/* Bundled Services */}
        {bundledServices.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center mb-2">
              <Gift className="h-4 w-4 text-green-600 mr-2" />
              <span className="text-sm font-medium text-green-800">Free Bundled Services</span>
            </div>
            {bundledServices.map((bundle, index) => (
              <div key={index} className="text-xs text-green-700 ml-6">
                • {bundle.service.name} (with {bundle.fromService.name})
              </div>
            ))}
            <div className="text-xs text-green-600 mt-2 font-medium">
              Value: KSh {cartState.bundledValue.toLocaleString()}
            </div>
          </div>
        )}
      </div>

      {/* Discount Section */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-2 mb-3">
          <Percent className="h-4 w-4 text-gray-600" />
          <input
            type="number"
            value={discountInput}
            onChange={(e) => setDiscountInput(e.target.value)}
            placeholder="Discount amount"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm"
            step="0.01"
            min="0"
          />
          <button
            onClick={handleDiscountApply}
            className="px-3 py-2 bg-orange-600 text-white rounded-lg text-sm hover:bg-orange-700"
          >
            Apply
          </button>
        </div>
      </div>

      {/* Totals */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Subtotal:</span>
            <span>KSh {cartState.subtotal.toLocaleString()}</span>
          </div>
          {cartState.bundledValue > 0 && (
            <div className="flex justify-between text-sm text-green-600">
              <span>Bundled Services:</span>
              <span>KSh {cartState.bundledValue.toLocaleString()}</span>
            </div>
          )}
          {cartState.discount > 0 && (
            <div className="flex justify-between text-sm text-orange-600">
              <span>Discount:</span>
              <span>-KSh {cartState.discount.toLocaleString()}</span>
            </div>
          )}
          <div className="flex justify-between text-lg font-bold border-t pt-2">
            <span>Total:</span>
            <span>KSh {cartState.total.toLocaleString()}</span>
          </div>
        </div>

        {/* Checkout Button */}
        <button
          onClick={() => setShowCheckout(true)}
          disabled={transactionLoading || cart.isEmpty}
          className="w-full mt-4 bg-primary-600 text-white py-3 rounded-lg font-medium hover:bg-primary-700 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {transactionLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </>
          ) : (
            <>
              <Receipt className="h-4 w-4 mr-2" />
              Proceed to Checkout
            </>
          )}
        </button>
      </div>

      {/* Checkout Modal */}
      {showCheckout && (
        <CheckoutModal
          cartState={cartState}
          onClose={() => setShowCheckout(false)}
          onComplete={async (paymentData) => {
            try {
              if (!currentUser) {
                alert('Error: User not authenticated');
                return;
              }

              // Save transaction to Firebase
              const transactionId = await saveTransaction(
                cartState,
                paymentData,
                currentUser.id
              );

              // Generate and print receipt
              const receiptData = {
                transaction: {
                  id: transactionId,
                  items: cartState.items.map(item => ({
                    id: item.id,
                    type: item.type,
                    itemId: item.itemId,
                    name: item.name,
                    quantity: item.quantity,
                    unitPrice: item.unitPrice,
                    totalPrice: item.totalPrice,
                    notes: item.notes,
                  })),
                  subtotal: cartState.subtotal,
                  discount: cartState.discount,
                  total: cartState.total,
                  paymentMethod: paymentData.paymentMethod,
                  customerId: paymentData.customerName ? 'temp-customer' : undefined,
                  attendantId: currentUser.id,
                  status: 'completed' as const,
                  notes: paymentData.notes,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                },
                attendant: currentUser,
                businessInfo: {
                  name: 'Cyber Services & Stationery',
                  address: '123 Main Street, Nairobi',
                  phone: '+254 700 123 456',
                  email: '<EMAIL>',
                },
              };

              // Print receipt
              printReceipt(receiptData);

              // Clear cart and close modal
              cart.clearCart();
              setShowCheckout(false);

              alert('Transaction completed successfully!');
            } catch (error) {
              console.error('Transaction error:', error);
              alert('Error completing transaction. Please try again.');
            }
          }}
        />
      )}
    </div>
  );
};

export default POSCart;
