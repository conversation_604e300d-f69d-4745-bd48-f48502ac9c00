{"ast": null, "code": "/**\n * Calculate the effective price of a service including bundled services\n */\nexport const calculateServicePrice = (service, allServices, customPrice) => {\n  var _service$bundledServi;\n  const originalPrice = customPrice !== null && customPrice !== void 0 ? customPrice : service.basePrice;\n\n  // Get bundled services\n  const bundledServices = ((_service$bundledServi = service.bundledServices) === null || _service$bundledServi === void 0 ? void 0 : _service$bundledServi.map(id => allServices.find(s => s.id === id)).filter(Boolean)) || [];\n\n  // Calculate bundled value (services that come free)\n  const bundledValue = bundledServices.reduce((total, bundledService) => {\n    return total + bundledService.basePrice;\n  }, 0);\n  return {\n    service,\n    originalPrice,\n    finalPrice: originalPrice,\n    // Main service price doesn't change\n    bundledServices,\n    bundledValue,\n    discount: bundledValue // The value of free services is the discount\n  };\n};\n\n/**\n * Calculate total for multiple services, considering bundles\n */\nexport const calculateServicesTotal = (selectedServices, allServices) => {\n  const items = selectedServices.map(({\n    service,\n    customPrice,\n    quantity = 1\n  }) => {\n    const calculation = calculateServicePrice(service, allServices, customPrice);\n    return {\n      ...calculation,\n      quantity\n    };\n  });\n  const subtotal = items.reduce((total, item) => {\n    return total + item.finalPrice * item.quantity;\n  }, 0);\n  const totalBundledValue = items.reduce((total, item) => {\n    return total + item.bundledValue * item.quantity;\n  }, 0);\n  return {\n    items,\n    subtotal,\n    totalBundledValue,\n    totalDiscount: totalBundledValue,\n    finalTotal: subtotal\n  };\n};\n\n/**\n * Get services by category with active filter\n */\nexport const getServicesByCategory = (services, category, activeOnly = true) => {\n  return services.filter(service => {\n    const matchesCategory = !category || service.category === category;\n    const matchesActive = !activeOnly || service.isActive;\n    return matchesCategory && matchesActive;\n  });\n};\n\n/**\n * Search services by name or description\n */\nexport const searchServices = (services, searchTerm, activeOnly = true) => {\n  if (!searchTerm.trim()) return activeOnly ? services.filter(s => s.isActive) : services;\n  const term = searchTerm.toLowerCase();\n  return services.filter(service => {\n    const matchesSearch = service.name.toLowerCase().includes(term) || service.description.toLowerCase().includes(term) || service.category.toLowerCase().includes(term);\n    const matchesActive = !activeOnly || service.isActive;\n    return matchesSearch && matchesActive;\n  });\n};\n\n/**\n * Get popular services based on usage (placeholder for now)\n */\nexport const getPopularServices = (services, limit = 5) => {\n  // For now, return active services sorted by name\n  // In a real app, this would be based on transaction data\n  return services.filter(service => service.isActive).sort((a, b) => a.name.localeCompare(b.name)).slice(0, limit);\n};\n\n/**\n * Validate service data\n */\nexport const validateService = service => {\n  var _service$name, _service$category;\n  const errors = [];\n  if (!((_service$name = service.name) !== null && _service$name !== void 0 && _service$name.trim())) {\n    errors.push('Service name is required');\n  }\n  if (!((_service$category = service.category) !== null && _service$category !== void 0 && _service$category.trim())) {\n    errors.push('Category is required');\n  }\n  if (service.basePrice === undefined || service.basePrice < 0) {\n    errors.push('Base price must be a positive number');\n  }\n  if (service.name && service.name.length > 100) {\n    errors.push('Service name must be less than 100 characters');\n  }\n  if (service.description && service.description.length > 500) {\n    errors.push('Description must be less than 500 characters');\n  }\n  return errors;\n};\n\n/**\n * Format price for display\n */\nexport const formatPrice = (price, currency = 'KSh') => {\n  return `${currency} ${price.toLocaleString()}`;\n};\n\n/**\n * Get service categories with counts\n */\nexport const getServiceCategoriesWithCounts = services => {\n  const categoryMap = new Map();\n  services.forEach(service => {\n    const current = categoryMap.get(service.category) || {\n      total: 0,\n      active: 0\n    };\n    current.total++;\n    if (service.isActive) current.active++;\n    categoryMap.set(service.category, current);\n  });\n  return Array.from(categoryMap.entries()).map(([category, counts]) => ({\n    category,\n    count: counts.total,\n    activeCount: counts.active\n  })).sort((a, b) => a.category.localeCompare(b.category));\n};", "map": {"version": 3, "names": ["calculateServicePrice", "service", "allServices", "customPrice", "_service$bundledServi", "originalPrice", "basePrice", "bundledServices", "map", "id", "find", "s", "filter", "Boolean", "bundledValue", "reduce", "total", "bundledService", "finalPrice", "discount", "calculateServicesTotal", "selectedServices", "items", "quantity", "calculation", "subtotal", "item", "totalBundledValue", "totalDiscount", "finalTotal", "getServicesByCategory", "services", "category", "activeOnly", "matchesCategory", "matchesActive", "isActive", "searchServices", "searchTerm", "trim", "term", "toLowerCase", "matchesSearch", "name", "includes", "description", "getPopularServices", "limit", "sort", "a", "b", "localeCompare", "slice", "validateService", "_service$name", "_service$category", "errors", "push", "undefined", "length", "formatPrice", "price", "currency", "toLocaleString", "getServiceCategoriesWithCounts", "categoryMap", "Map", "for<PERSON>ach", "current", "get", "active", "set", "Array", "from", "entries", "counts", "count", "activeCount"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/serviceUtils.ts"], "sourcesContent": ["import { Service } from '../types';\n\nexport interface ServiceCalculation {\n  service: Service;\n  originalPrice: number;\n  finalPrice: number;\n  bundledServices: Service[];\n  bundledValue: number;\n  discount: number;\n}\n\n/**\n * Calculate the effective price of a service including bundled services\n */\nexport const calculateServicePrice = (\n  service: Service,\n  allServices: Service[],\n  customPrice?: number\n): ServiceCalculation => {\n  const originalPrice = customPrice ?? service.basePrice;\n  \n  // Get bundled services\n  const bundledServices = service.bundledServices\n    ?.map(id => allServices.find(s => s.id === id))\n    .filter(Boolean) as Service[] || [];\n  \n  // Calculate bundled value (services that come free)\n  const bundledValue = bundledServices.reduce((total, bundledService) => {\n    return total + bundledService.basePrice;\n  }, 0);\n  \n  return {\n    service,\n    originalPrice,\n    finalPrice: originalPrice, // Main service price doesn't change\n    bundledServices,\n    bundledValue,\n    discount: bundledValue // The value of free services is the discount\n  };\n};\n\n/**\n * Calculate total for multiple services, considering bundles\n */\nexport const calculateServicesTotal = (\n  selectedServices: Array<{ service: Service; customPrice?: number; quantity?: number }>,\n  allServices: Service[]\n): {\n  items: Array<ServiceCalculation & { quantity: number }>;\n  subtotal: number;\n  totalBundledValue: number;\n  totalDiscount: number;\n  finalTotal: number;\n} => {\n  const items = selectedServices.map(({ service, customPrice, quantity = 1 }) => {\n    const calculation = calculateServicePrice(service, allServices, customPrice);\n    return { ...calculation, quantity };\n  });\n  \n  const subtotal = items.reduce((total, item) => {\n    return total + (item.finalPrice * item.quantity);\n  }, 0);\n  \n  const totalBundledValue = items.reduce((total, item) => {\n    return total + (item.bundledValue * item.quantity);\n  }, 0);\n  \n  return {\n    items,\n    subtotal,\n    totalBundledValue,\n    totalDiscount: totalBundledValue,\n    finalTotal: subtotal\n  };\n};\n\n/**\n * Get services by category with active filter\n */\nexport const getServicesByCategory = (\n  services: Service[],\n  category?: string,\n  activeOnly: boolean = true\n): Service[] => {\n  return services.filter(service => {\n    const matchesCategory = !category || service.category === category;\n    const matchesActive = !activeOnly || service.isActive;\n    return matchesCategory && matchesActive;\n  });\n};\n\n/**\n * Search services by name or description\n */\nexport const searchServices = (\n  services: Service[],\n  searchTerm: string,\n  activeOnly: boolean = true\n): Service[] => {\n  if (!searchTerm.trim()) return activeOnly ? services.filter(s => s.isActive) : services;\n  \n  const term = searchTerm.toLowerCase();\n  return services.filter(service => {\n    const matchesSearch = \n      service.name.toLowerCase().includes(term) ||\n      service.description.toLowerCase().includes(term) ||\n      service.category.toLowerCase().includes(term);\n    const matchesActive = !activeOnly || service.isActive;\n    return matchesSearch && matchesActive;\n  });\n};\n\n/**\n * Get popular services based on usage (placeholder for now)\n */\nexport const getPopularServices = (\n  services: Service[],\n  limit: number = 5\n): Service[] => {\n  // For now, return active services sorted by name\n  // In a real app, this would be based on transaction data\n  return services\n    .filter(service => service.isActive)\n    .sort((a, b) => a.name.localeCompare(b.name))\n    .slice(0, limit);\n};\n\n/**\n * Validate service data\n */\nexport const validateService = (service: Partial<Service>): string[] => {\n  const errors: string[] = [];\n  \n  if (!service.name?.trim()) {\n    errors.push('Service name is required');\n  }\n  \n  if (!service.category?.trim()) {\n    errors.push('Category is required');\n  }\n  \n  if (service.basePrice === undefined || service.basePrice < 0) {\n    errors.push('Base price must be a positive number');\n  }\n  \n  if (service.name && service.name.length > 100) {\n    errors.push('Service name must be less than 100 characters');\n  }\n  \n  if (service.description && service.description.length > 500) {\n    errors.push('Description must be less than 500 characters');\n  }\n  \n  return errors;\n};\n\n/**\n * Format price for display\n */\nexport const formatPrice = (price: number, currency: string = 'KSh'): string => {\n  return `${currency} ${price.toLocaleString()}`;\n};\n\n/**\n * Get service categories with counts\n */\nexport const getServiceCategoriesWithCounts = (services: Service[]): Array<{\n  category: string;\n  count: number;\n  activeCount: number;\n}> => {\n  const categoryMap = new Map<string, { total: number; active: number }>();\n  \n  services.forEach(service => {\n    const current = categoryMap.get(service.category) || { total: 0, active: 0 };\n    current.total++;\n    if (service.isActive) current.active++;\n    categoryMap.set(service.category, current);\n  });\n  \n  return Array.from(categoryMap.entries())\n    .map(([category, counts]) => ({\n      category,\n      count: counts.total,\n      activeCount: counts.active\n    }))\n    .sort((a, b) => a.category.localeCompare(b.category));\n};\n"], "mappings": "AAWA;AACA;AACA;AACA,OAAO,MAAMA,qBAAqB,GAAGA,CACnCC,OAAgB,EAChBC,WAAsB,EACtBC,WAAoB,KACG;EAAA,IAAAC,qBAAA;EACvB,MAAMC,aAAa,GAAGF,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAIF,OAAO,CAACK,SAAS;;EAEtD;EACA,MAAMC,eAAe,GAAG,EAAAH,qBAAA,GAAAH,OAAO,CAACM,eAAe,cAAAH,qBAAA,uBAAvBA,qBAAA,CACpBI,GAAG,CAACC,EAAE,IAAIP,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKA,EAAE,CAAC,CAAC,CAC9CG,MAAM,CAACC,OAAO,CAAC,KAAiB,EAAE;;EAErC;EACA,MAAMC,YAAY,GAAGP,eAAe,CAACQ,MAAM,CAAC,CAACC,KAAK,EAAEC,cAAc,KAAK;IACrE,OAAOD,KAAK,GAAGC,cAAc,CAACX,SAAS;EACzC,CAAC,EAAE,CAAC,CAAC;EAEL,OAAO;IACLL,OAAO;IACPI,aAAa;IACba,UAAU,EAAEb,aAAa;IAAE;IAC3BE,eAAe;IACfO,YAAY;IACZK,QAAQ,EAAEL,YAAY,CAAC;EACzB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMM,sBAAsB,GAAGA,CACpCC,gBAAsF,EACtFnB,WAAsB,KAOnB;EACH,MAAMoB,KAAK,GAAGD,gBAAgB,CAACb,GAAG,CAAC,CAAC;IAAEP,OAAO;IAAEE,WAAW;IAAEoB,QAAQ,GAAG;EAAE,CAAC,KAAK;IAC7E,MAAMC,WAAW,GAAGxB,qBAAqB,CAACC,OAAO,EAAEC,WAAW,EAAEC,WAAW,CAAC;IAC5E,OAAO;MAAE,GAAGqB,WAAW;MAAED;IAAS,CAAC;EACrC,CAAC,CAAC;EAEF,MAAME,QAAQ,GAAGH,KAAK,CAACP,MAAM,CAAC,CAACC,KAAK,EAAEU,IAAI,KAAK;IAC7C,OAAOV,KAAK,GAAIU,IAAI,CAACR,UAAU,GAAGQ,IAAI,CAACH,QAAS;EAClD,CAAC,EAAE,CAAC,CAAC;EAEL,MAAMI,iBAAiB,GAAGL,KAAK,CAACP,MAAM,CAAC,CAACC,KAAK,EAAEU,IAAI,KAAK;IACtD,OAAOV,KAAK,GAAIU,IAAI,CAACZ,YAAY,GAAGY,IAAI,CAACH,QAAS;EACpD,CAAC,EAAE,CAAC,CAAC;EAEL,OAAO;IACLD,KAAK;IACLG,QAAQ;IACRE,iBAAiB;IACjBC,aAAa,EAAED,iBAAiB;IAChCE,UAAU,EAAEJ;EACd,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMK,qBAAqB,GAAGA,CACnCC,QAAmB,EACnBC,QAAiB,EACjBC,UAAmB,GAAG,IAAI,KACZ;EACd,OAAOF,QAAQ,CAACnB,MAAM,CAACX,OAAO,IAAI;IAChC,MAAMiC,eAAe,GAAG,CAACF,QAAQ,IAAI/B,OAAO,CAAC+B,QAAQ,KAAKA,QAAQ;IAClE,MAAMG,aAAa,GAAG,CAACF,UAAU,IAAIhC,OAAO,CAACmC,QAAQ;IACrD,OAAOF,eAAe,IAAIC,aAAa;EACzC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,cAAc,GAAGA,CAC5BN,QAAmB,EACnBO,UAAkB,EAClBL,UAAmB,GAAG,IAAI,KACZ;EACd,IAAI,CAACK,UAAU,CAACC,IAAI,CAAC,CAAC,EAAE,OAAON,UAAU,GAAGF,QAAQ,CAACnB,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACyB,QAAQ,CAAC,GAAGL,QAAQ;EAEvF,MAAMS,IAAI,GAAGF,UAAU,CAACG,WAAW,CAAC,CAAC;EACrC,OAAOV,QAAQ,CAACnB,MAAM,CAACX,OAAO,IAAI;IAChC,MAAMyC,aAAa,GACjBzC,OAAO,CAAC0C,IAAI,CAACF,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,IAAI,CAAC,IACzCvC,OAAO,CAAC4C,WAAW,CAACJ,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,IAAI,CAAC,IAChDvC,OAAO,CAAC+B,QAAQ,CAACS,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,IAAI,CAAC;IAC/C,MAAML,aAAa,GAAG,CAACF,UAAU,IAAIhC,OAAO,CAACmC,QAAQ;IACrD,OAAOM,aAAa,IAAIP,aAAa;EACvC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMW,kBAAkB,GAAGA,CAChCf,QAAmB,EACnBgB,KAAa,GAAG,CAAC,KACH;EACd;EACA;EACA,OAAOhB,QAAQ,CACZnB,MAAM,CAACX,OAAO,IAAIA,OAAO,CAACmC,QAAQ,CAAC,CACnCY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACN,IAAI,CAACQ,aAAa,CAACD,CAAC,CAACP,IAAI,CAAC,CAAC,CAC5CS,KAAK,CAAC,CAAC,EAAEL,KAAK,CAAC;AACpB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMM,eAAe,GAAIpD,OAAyB,IAAe;EAAA,IAAAqD,aAAA,EAAAC,iBAAA;EACtE,MAAMC,MAAgB,GAAG,EAAE;EAE3B,IAAI,GAAAF,aAAA,GAACrD,OAAO,CAAC0C,IAAI,cAAAW,aAAA,eAAZA,aAAA,CAAcf,IAAI,CAAC,CAAC,GAAE;IACzBiB,MAAM,CAACC,IAAI,CAAC,0BAA0B,CAAC;EACzC;EAEA,IAAI,GAAAF,iBAAA,GAACtD,OAAO,CAAC+B,QAAQ,cAAAuB,iBAAA,eAAhBA,iBAAA,CAAkBhB,IAAI,CAAC,CAAC,GAAE;IAC7BiB,MAAM,CAACC,IAAI,CAAC,sBAAsB,CAAC;EACrC;EAEA,IAAIxD,OAAO,CAACK,SAAS,KAAKoD,SAAS,IAAIzD,OAAO,CAACK,SAAS,GAAG,CAAC,EAAE;IAC5DkD,MAAM,CAACC,IAAI,CAAC,sCAAsC,CAAC;EACrD;EAEA,IAAIxD,OAAO,CAAC0C,IAAI,IAAI1C,OAAO,CAAC0C,IAAI,CAACgB,MAAM,GAAG,GAAG,EAAE;IAC7CH,MAAM,CAACC,IAAI,CAAC,+CAA+C,CAAC;EAC9D;EAEA,IAAIxD,OAAO,CAAC4C,WAAW,IAAI5C,OAAO,CAAC4C,WAAW,CAACc,MAAM,GAAG,GAAG,EAAE;IAC3DH,MAAM,CAACC,IAAI,CAAC,8CAA8C,CAAC;EAC7D;EAEA,OAAOD,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMI,WAAW,GAAGA,CAACC,KAAa,EAAEC,QAAgB,GAAG,KAAK,KAAa;EAC9E,OAAO,GAAGA,QAAQ,IAAID,KAAK,CAACE,cAAc,CAAC,CAAC,EAAE;AAChD,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,8BAA8B,GAAIjC,QAAmB,IAI5D;EACJ,MAAMkC,WAAW,GAAG,IAAIC,GAAG,CAA4C,CAAC;EAExEnC,QAAQ,CAACoC,OAAO,CAAClE,OAAO,IAAI;IAC1B,MAAMmE,OAAO,GAAGH,WAAW,CAACI,GAAG,CAACpE,OAAO,CAAC+B,QAAQ,CAAC,IAAI;MAAEhB,KAAK,EAAE,CAAC;MAAEsD,MAAM,EAAE;IAAE,CAAC;IAC5EF,OAAO,CAACpD,KAAK,EAAE;IACf,IAAIf,OAAO,CAACmC,QAAQ,EAAEgC,OAAO,CAACE,MAAM,EAAE;IACtCL,WAAW,CAACM,GAAG,CAACtE,OAAO,CAAC+B,QAAQ,EAAEoC,OAAO,CAAC;EAC5C,CAAC,CAAC;EAEF,OAAOI,KAAK,CAACC,IAAI,CAACR,WAAW,CAACS,OAAO,CAAC,CAAC,CAAC,CACrClE,GAAG,CAAC,CAAC,CAACwB,QAAQ,EAAE2C,MAAM,CAAC,MAAM;IAC5B3C,QAAQ;IACR4C,KAAK,EAAED,MAAM,CAAC3D,KAAK;IACnB6D,WAAW,EAAEF,MAAM,CAACL;EACtB,CAAC,CAAC,CAAC,CACFtB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjB,QAAQ,CAACmB,aAAa,CAACD,CAAC,CAAClB,QAAQ,CAAC,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}