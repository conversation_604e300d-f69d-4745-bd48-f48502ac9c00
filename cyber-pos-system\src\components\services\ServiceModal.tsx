import React from 'react';
import { X, DollarSign, Tag, Link } from 'lucide-react';
import { Service } from '../../types';

interface ServiceModalProps {
  service: Service | null;
  formData: {
    name: string;
    description: string;
    basePrice: number;
    category: string;
    allowPriceOverride: boolean;
    bundledServices: string[];
    isActive: boolean;
  };
  setFormData: React.Dispatch<React.SetStateAction<{
    name: string;
    description: string;
    basePrice: number;
    category: string;
    allowPriceOverride: boolean;
    bundledServices: string[];
    isActive: boolean;
  }>>;
  onSubmit: (e: React.FormEvent) => void;
  onClose: () => void;
  allServices: Service[];
}

const ServiceModal: React.FC<ServiceModalProps> = ({
  service,
  formData,
  setFormData,
  onSubmit,
  onClose,
  allServices
}) => {
  const isEditing = !!service;
  const availableServices = allServices.filter(s => s.id !== service?.id && s.isActive);
  
  const predefinedCategories = [
    'Printing',
    'Scanning', 
    'Typing',
    'Internet',
    'Government',
    'Office',
    'Other'
  ];

  const handleBundledServiceToggle = (serviceId: string) => {
    const currentBundled = formData.bundledServices || [];
    if (currentBundled.includes(serviceId)) {
      setFormData({
        ...formData,
        bundledServices: currentBundled.filter(id => id !== serviceId)
      });
    } else {
      setFormData({
        ...formData,
        bundledServices: [...currentBundled, serviceId]
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">
              {isEditing ? 'Edit Service' : 'Create New Service'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          <form onSubmit={onSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Service Name *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., Document Printing"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category *
                </label>
                <select
                  required
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Select Category</option>
                  {predefinedCategories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Brief description of the service"
              />
            </div>

            {/* Pricing */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                Pricing Configuration
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Base Price (KSh) *
                  </label>
                  <input
                    type="number"
                    required
                    min="0"
                    step="0.01"
                    value={formData.basePrice}
                    onChange={(e) => setFormData({ ...formData, basePrice: parseFloat(e.target.value) || 0 })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="allowPriceOverride"
                    checked={formData.allowPriceOverride}
                    onChange={(e) => setFormData({ ...formData, allowPriceOverride: e.target.checked })}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="allowPriceOverride" className="ml-2 block text-sm text-gray-700">
                    Allow price override at POS
                  </label>
                </div>
              </div>
            </div>

            {/* Service Bundling */}
            {availableServices.length > 0 && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                  <Link className="h-4 w-4 mr-2" />
                  Service Bundling
                </h4>
                <p className="text-xs text-gray-600 mb-3">
                  Select services that come free with this service (e.g., free emailing with typing)
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                  {availableServices.map(availableService => (
                    <label key={availableService.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.bundledServices?.includes(availableService.id) || false}
                        onChange={() => handleBundledServiceToggle(availableService.id)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        {availableService.name} (KSh {availableService.basePrice})
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            )}

            {/* Status */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                Service is active and available for sale
              </label>
            </div>
            
            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                {isEditing ? 'Update Service' : 'Create Service'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ServiceModal;
