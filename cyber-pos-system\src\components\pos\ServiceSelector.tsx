import React, { useState } from 'react';
import {
  Plus,
  Monitor,
  DollarSign,
  Edit3,
  Link,
  Check,
  X,
  ShoppingCart
} from 'lucide-react';
import { Service } from '../../types';

interface ServiceSelectorProps {
  services: Service[];
  loading: boolean;
  viewMode: 'grid' | 'list';
  onAddToCart: (service: Service, customPrice?: number, notes?: string) => void;
  cart: {
    isInCart: (itemId: string, type: 'service' | 'product') => boolean;
    getItemQuantity: (itemId: string, type: 'service' | 'product') => number;
  };
}

const ServiceSelector: React.FC<ServiceSelectorProps> = ({
  services,
  loading,
  viewMode,
  onAddToCart,
  cart
}) => {
  const [customPrices, setCustomPrices] = useState<Record<string, string>>({});
  const [notes, setNotes] = useState<Record<string, string>>({});
  const [editingPrice, setEditingPrice] = useState<string | null>(null);
  const [editingNotes, setEditingNotes] = useState<string | null>(null);

  const handleAddToCart = (service: Service) => {
    const customPrice = customPrices[service.id] ? parseFloat(customPrices[service.id]) : undefined;
    const serviceNotes = notes[service.id] || undefined;
    
    onAddToCart(service, customPrice, serviceNotes);
    
    // Clear custom inputs after adding
    setCustomPrices(prev => ({ ...prev, [service.id]: '' }));
    setNotes(prev => ({ ...prev, [service.id]: '' }));
  };

  const handlePriceChange = (serviceId: string, value: string) => {
    setCustomPrices(prev => ({ ...prev, [serviceId]: value }));
  };

  const handleNotesChange = (serviceId: string, value: string) => {
    setNotes(prev => ({ ...prev, [serviceId]: value }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-gray-600">Loading services...</span>
      </div>
    );
  }

  if (services.length === 0) {
    return (
      <div className="text-center py-12">
        <Monitor className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No services found</h3>
        <p className="mt-1 text-sm text-gray-500">
          Try adjusting your search or filter criteria.
        </p>
      </div>
    );
  }

  if (viewMode === 'list') {
    return (
      <div className="space-y-2">
        {services.map((service) => (
          <ServiceListItem
            key={service.id}
            service={service}
            customPrice={customPrices[service.id] || ''}
            notes={notes[service.id] || ''}
            editingPrice={editingPrice === service.id}
            editingNotes={editingNotes === service.id}
            onPriceChange={(value) => handlePriceChange(service.id, value)}
            onNotesChange={(value) => handleNotesChange(service.id, value)}
            onEditPrice={() => setEditingPrice(service.id)}
            onEditNotes={() => setEditingNotes(service.id)}
            onCancelEdit={() => {
              setEditingPrice(null);
              setEditingNotes(null);
            }}
            onAddToCart={() => handleAddToCart(service)}
            isInCart={cart.isInCart(service.id, 'service')}
            cartQuantity={cart.getItemQuantity(service.id, 'service')}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {services.map((service) => (
        <ServiceCard
          key={service.id}
          service={service}
          customPrice={customPrices[service.id] || ''}
          notes={notes[service.id] || ''}
          editingPrice={editingPrice === service.id}
          editingNotes={editingNotes === service.id}
          onPriceChange={(value) => handlePriceChange(service.id, value)}
          onNotesChange={(value) => handleNotesChange(service.id, value)}
          onEditPrice={() => setEditingPrice(service.id)}
          onEditNotes={() => setEditingNotes(service.id)}
          onCancelEdit={() => {
            setEditingPrice(null);
            setEditingNotes(null);
          }}
          onAddToCart={() => handleAddToCart(service)}
          isInCart={cart.isInCart(service.id, 'service')}
          cartQuantity={cart.getItemQuantity(service.id, 'service')}
        />
      ))}
    </div>
  );
};

// Service Card Component
interface ServiceItemProps {
  service: Service;
  customPrice: string;
  notes: string;
  editingPrice: boolean;
  editingNotes: boolean;
  onPriceChange: (value: string) => void;
  onNotesChange: (value: string) => void;
  onEditPrice: () => void;
  onEditNotes: () => void;
  onCancelEdit: () => void;
  onAddToCart: () => void;
  isInCart: boolean;
  cartQuantity: number;
}

const ServiceCard: React.FC<ServiceItemProps> = ({
  service,
  customPrice,
  notes,
  editingPrice,
  editingNotes,
  onPriceChange,
  onNotesChange,
  onEditPrice,
  onEditNotes,
  onCancelEdit,
  onAddToCart,
  isInCart,
  cartQuantity
}) => {
  const effectivePrice = customPrice ? parseFloat(customPrice) : service.basePrice;
  const hasCustomPrice = customPrice && parseFloat(customPrice) !== service.basePrice;

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900">{service.name}</h3>
          <p className="text-sm text-gray-600 mt-1">{service.description}</p>
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 mt-2">
            {service.category}
          </span>
        </div>
        {isInCart && (
          <div className="flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
            <ShoppingCart className="h-3 w-3 mr-1" />
            {cartQuantity}
          </div>
        )}
      </div>

      {/* Bundled Services */}
      {service.bundledServices && service.bundledServices.length > 0 && (
        <div className="mb-3 p-2 bg-green-50 rounded border border-green-200">
          <div className="flex items-center text-xs text-green-700 mb-1">
            <Link className="h-3 w-3 mr-1" />
            <span className="font-medium">Includes free services</span>
          </div>
        </div>
      )}

      {/* Price Section */}
      <div className="mb-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Price:</span>
          {editingPrice ? (
            <div className="flex items-center space-x-2">
              <input
                type="number"
                value={customPrice}
                onChange={(e) => onPriceChange(e.target.value)}
                className="w-20 px-2 py-1 text-sm border border-gray-300 rounded"
                step="0.01"
                min="0"
                placeholder={service.basePrice.toString()}
              />
              <button onClick={onCancelEdit} className="text-green-600 hover:text-green-800">
                <Check className="h-4 w-4" />
              </button>
              <button onClick={onCancelEdit} className="text-gray-600 hover:text-gray-800">
                <X className="h-4 w-4" />
              </button>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <span className={`font-semibold ${hasCustomPrice ? 'text-orange-600' : 'text-green-600'}`}>
                KSh {effectivePrice.toLocaleString()}
              </span>
              {service.allowPriceOverride && (
                <button
                  onClick={onEditPrice}
                  className="text-blue-600 hover:text-blue-800"
                  title="Edit price"
                >
                  <Edit3 className="h-3 w-3" />
                </button>
              )}
            </div>
          )}
        </div>
        {hasCustomPrice && (
          <div className="text-xs text-gray-500 mt-1">
            Original: KSh {service.basePrice.toLocaleString()}
          </div>
        )}
      </div>

      {/* Notes Section */}
      <div className="mb-4">
        {editingNotes ? (
          <div className="space-y-2">
            <input
              type="text"
              value={notes}
              onChange={(e) => onNotesChange(e.target.value)}
              placeholder="Add notes for this service..."
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
            />
            <div className="flex justify-end space-x-2">
              <button onClick={onCancelEdit} className="text-green-600 hover:text-green-800">
                <Check className="h-4 w-4" />
              </button>
              <button onClick={onCancelEdit} className="text-gray-600 hover:text-gray-800">
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        ) : (
          <div>
            {notes ? (
              <div className="text-xs text-gray-600 italic bg-gray-50 p-2 rounded">
                "{notes}"
                <button
                  onClick={onEditNotes}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  <Edit3 className="h-3 w-3" />
                </button>
              </div>
            ) : (
              <button
                onClick={onEditNotes}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                Add notes
              </button>
            )}
          </div>
        )}
      </div>

      {/* Add to Cart Button */}
      <button
        onClick={onAddToCart}
        className="w-full bg-primary-600 text-white py-2 rounded-lg font-medium hover:bg-primary-700 flex items-center justify-center"
      >
        <Plus className="h-4 w-4 mr-2" />
        Add to Cart
      </button>
    </div>
  );
};

// Service List Item Component (for list view)
const ServiceListItem: React.FC<ServiceItemProps> = ({
  service,
  customPrice,
  notes,
  editingPrice,
  editingNotes,
  onPriceChange,
  onNotesChange,
  onEditPrice,
  onEditNotes,
  onCancelEdit,
  onAddToCart,
  isInCart,
  cartQuantity
}) => {
  const effectivePrice = customPrice ? parseFloat(customPrice) : service.basePrice;
  const hasCustomPrice = customPrice && parseFloat(customPrice) !== service.basePrice;

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 flex items-center space-x-4">
      <div className="flex-1">
        <div className="flex items-center space-x-3">
          <h3 className="font-semibold text-gray-900">{service.name}</h3>
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
            {service.category}
          </span>
          {isInCart && (
            <div className="flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
              <ShoppingCart className="h-3 w-3 mr-1" />
              {cartQuantity}
            </div>
          )}
        </div>
        <p className="text-sm text-gray-600 mt-1">{service.description}</p>
      </div>

      <div className="flex items-center space-x-4">
        {/* Price */}
        <div className="text-right">
          {editingPrice ? (
            <div className="flex items-center space-x-2">
              <input
                type="number"
                value={customPrice}
                onChange={(e) => onPriceChange(e.target.value)}
                className="w-20 px-2 py-1 text-sm border border-gray-300 rounded"
                step="0.01"
                min="0"
                placeholder={service.basePrice.toString()}
              />
              <button onClick={onCancelEdit} className="text-green-600 hover:text-green-800">
                <Check className="h-4 w-4" />
              </button>
              <button onClick={onCancelEdit} className="text-gray-600 hover:text-gray-800">
                <X className="h-4 w-4" />
              </button>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <div>
                <div className={`font-semibold ${hasCustomPrice ? 'text-orange-600' : 'text-green-600'}`}>
                  KSh {effectivePrice.toLocaleString()}
                </div>
                {hasCustomPrice && (
                  <div className="text-xs text-gray-500">
                    Was: KSh {service.basePrice.toLocaleString()}
                  </div>
                )}
              </div>
              {service.allowPriceOverride && (
                <button
                  onClick={onEditPrice}
                  className="text-blue-600 hover:text-blue-800"
                  title="Edit price"
                >
                  <Edit3 className="h-3 w-3" />
                </button>
              )}
            </div>
          )}
        </div>

        {/* Add to Cart Button */}
        <button
          onClick={onAddToCart}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add
        </button>
      </div>
    </div>
  );
};

export default ServiceSelector;
