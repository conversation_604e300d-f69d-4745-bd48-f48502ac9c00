{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\services\\\\Services.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Monitor, Plus, Edit, Trash2, Search, Filter, ToggleLeft, ToggleRight, Link } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useServices } from '../../hooks/useServices';\nimport ServiceModal from './ServiceModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Services = () => {\n  _s();\n  const {\n    hasPermission\n  } = useAuth();\n  const {\n    services,\n    loading,\n    error,\n    createService,\n    updateService,\n    deleteService,\n    getServiceCategories\n  } = useServices();\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [editingService, setEditingService] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    basePrice: 0,\n    category: '',\n    allowPriceOverride: true,\n    bundledServices: [],\n    isActive: true\n  });\n\n  // Filter services based on search and category\n  const filteredServices = services.filter(service => {\n    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) || service.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !selectedCategory || service.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n  const categories = getServiceCategories();\n  const handleCreateService = async e => {\n    e.preventDefault();\n    try {\n      await createService(formData);\n      setShowCreateModal(false);\n      resetForm();\n    } catch (error) {\n      console.error('Error creating service:', error);\n    }\n  };\n  const handleUpdateService = async e => {\n    e.preventDefault();\n    if (!editingService) return;\n    try {\n      await updateService(editingService.id, formData);\n      setEditingService(null);\n      resetForm();\n    } catch (error) {\n      console.error('Error updating service:', error);\n    }\n  };\n  const handleDeleteService = async serviceId => {\n    if (window.confirm('Are you sure you want to delete this service?')) {\n      try {\n        await deleteService(serviceId);\n      } catch (error) {\n        console.error('Error deleting service:', error);\n      }\n    }\n  };\n  const handleToggleActive = async service => {\n    try {\n      await updateService(service.id, {\n        isActive: !service.isActive\n      });\n    } catch (error) {\n      console.error('Error toggling service status:', error);\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      basePrice: 0,\n      category: '',\n      allowPriceOverride: true,\n      bundledServices: [],\n      isActive: true\n    });\n  };\n  const openEditModal = service => {\n    setFormData({\n      name: service.name,\n      description: service.description,\n      basePrice: service.basePrice,\n      category: service.category,\n      allowPriceOverride: service.allowPriceOverride,\n      bundledServices: service.bundledServices || [],\n      isActive: service.isActive\n    });\n    setEditingService(service);\n    setShowCreateModal(true);\n  };\n  if (!hasPermission('admin')) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(Monitor, {\n        className: \"mx-auto h-12 w-12 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"mt-2 text-sm font-medium text-gray-900\",\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-500\",\n        children: \"You don't have permission to manage services.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Monitor, {\n            className: \"h-6 w-6 text-primary-600 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Services Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            resetForm();\n            setEditingService(null);\n            setShowCreateModal(true);\n          },\n          className: \"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), \"Add Service\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 relative\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search services...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: e => setSelectedCategory(e.target.value),\n            className: \"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category,\n              children: category\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-gray-500\",\n          children: \"Loading services...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this) : filteredServices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Monitor, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900\",\n          children: \"No services found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: searchTerm || selectedCategory ? 'Try adjusting your search or filter.' : 'Get started by creating your first service.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: filteredServices.map(service => /*#__PURE__*/_jsxDEV(ServiceCard, {\n          service: service,\n          onEdit: openEditModal,\n          onDelete: handleDeleteService,\n          onToggleActive: handleToggleActive,\n          allServices: services\n        }, service.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), showCreateModal && /*#__PURE__*/_jsxDEV(ServiceModal, {\n      service: editingService,\n      formData: formData,\n      setFormData: setFormData,\n      onSubmit: editingService ? handleUpdateService : handleCreateService,\n      onClose: () => {\n        setShowCreateModal(false);\n        setEditingService(null);\n        resetForm();\n      },\n      allServices: services\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n};\n\n// Service Card Component\n_s(Services, \"QjMT8wiXEsnBu9OyASrn4aVpzAU=\", false, function () {\n  return [useAuth, useServices];\n});\n_c = Services;\nconst ServiceCard = ({\n  service,\n  onEdit,\n  onDelete,\n  onToggleActive,\n  allServices\n}) => {\n  var _service$bundledServi;\n  const bundledServiceNames = ((_service$bundledServi = service.bundledServices) === null || _service$bundledServi === void 0 ? void 0 : _service$bundledServi.map(id => {\n    var _allServices$find;\n    return (_allServices$find = allServices.find(s => s.id === id)) === null || _allServices$find === void 0 ? void 0 : _allServices$find.name;\n  }).filter(Boolean)) || [];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-white border rounded-lg p-4 ${service.isActive ? 'border-gray-200' : 'border-gray-300 bg-gray-50'}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${service.isActive ? 'text-gray-900' : 'text-gray-500'}`,\n          children: service.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-sm ${service.isActive ? 'text-gray-600' : 'text-gray-400'}`,\n          children: service.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onEdit(service),\n          className: \"text-blue-600 hover:text-blue-800\",\n          children: /*#__PURE__*/_jsxDEV(Edit, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onDelete(service.id),\n          className: \"text-red-600 hover:text-red-800\",\n          children: /*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Category:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n          children: service.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Base Price:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-semibold text-green-600\",\n          children: [\"KSh \", service.basePrice.toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Price Override:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-xs ${service.allowPriceOverride ? 'text-green-600' : 'text-red-600'}`,\n          children: service.allowPriceOverride ? 'Allowed' : 'Not Allowed'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), bundledServiceNames.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Bundled Services:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-1 flex flex-wrap gap-1\",\n          children: bundledServiceNames.map((name, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-800 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              className: \"h-3 w-3 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this), name]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between pt-2 border-t\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Status:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onToggleActive(service),\n          className: \"flex items-center\",\n          children: [service.isActive ? /*#__PURE__*/_jsxDEV(ToggleRight, {\n            className: \"h-5 w-5 text-green-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ToggleLeft, {\n            className: \"h-5 w-5 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `ml-1 text-sm ${service.isActive ? 'text-green-600' : 'text-gray-500'}`,\n            children: service.isActive ? 'Active' : 'Inactive'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 263,\n    columnNumber: 5\n  }, this);\n};\n_c2 = ServiceCard;\nexport default Services;\nvar _c, _c2;\n$RefreshReg$(_c, \"Services\");\n$RefreshReg$(_c2, \"ServiceCard\");", "map": {"version": 3, "names": ["React", "useState", "Monitor", "Plus", "Edit", "Trash2", "Search", "Filter", "ToggleLeft", "ToggleRight", "Link", "useAuth", "useServices", "ServiceModal", "jsxDEV", "_jsxDEV", "Services", "_s", "hasPermission", "services", "loading", "error", "createService", "updateService", "deleteService", "getServiceCategories", "showCreateModal", "setShowCreateModal", "editingService", "setEditingService", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "formData", "setFormData", "name", "description", "basePrice", "category", "allowPriceOverride", "bundledServices", "isActive", "filteredServices", "filter", "service", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "categories", "handleCreateService", "e", "preventDefault", "resetForm", "console", "handleUpdateService", "id", "handleDeleteService", "serviceId", "window", "confirm", "handleToggleActive", "openEditModal", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "placeholder", "value", "onChange", "target", "map", "length", "ServiceCard", "onEdit", "onDelete", "onToggleActive", "allServices", "onSubmit", "onClose", "_c", "_service$bundledServi", "bundledServiceNames", "_allServices$find", "find", "s", "Boolean", "toLocaleString", "index", "_c2", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/services/Services.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Monitor,\n  Plus,\n  Edit,\n  Trash2,\n  Search,\n  Filter,\n  DollarSign,\n  Tag,\n  ToggleLeft,\n  ToggleRight,\n  Link,\n  X,\n  Save\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useServices } from '../../hooks/useServices';\nimport { Service } from '../../types';\nimport ServiceModal from './ServiceModal';\n\nconst Services: React.FC = () => {\n  const { hasPermission } = useAuth();\n  const {\n    services,\n    loading,\n    error,\n    createService,\n    updateService,\n    deleteService,\n    getServiceCategories\n  } = useServices();\n\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [editingService, setEditingService] = useState<Service | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    basePrice: 0,\n    category: '',\n    allowPriceOverride: true,\n    bundledServices: [] as string[],\n    isActive: true\n  });\n\n  // Filter services based on search and category\n  const filteredServices = services.filter(service => {\n    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         service.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !selectedCategory || service.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const categories = getServiceCategories();\n\n  const handleCreateService = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      await createService(formData);\n      setShowCreateModal(false);\n      resetForm();\n    } catch (error) {\n      console.error('Error creating service:', error);\n    }\n  };\n\n  const handleUpdateService = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!editingService) return;\n\n    try {\n      await updateService(editingService.id, formData);\n      setEditingService(null);\n      resetForm();\n    } catch (error) {\n      console.error('Error updating service:', error);\n    }\n  };\n\n  const handleDeleteService = async (serviceId: string) => {\n    if (window.confirm('Are you sure you want to delete this service?')) {\n      try {\n        await deleteService(serviceId);\n      } catch (error) {\n        console.error('Error deleting service:', error);\n      }\n    }\n  };\n\n  const handleToggleActive = async (service: Service) => {\n    try {\n      await updateService(service.id, { isActive: !service.isActive });\n    } catch (error) {\n      console.error('Error toggling service status:', error);\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      basePrice: 0,\n      category: '',\n      allowPriceOverride: true,\n      bundledServices: [],\n      isActive: true\n    });\n  };\n\n  const openEditModal = (service: Service) => {\n    setFormData({\n      name: service.name,\n      description: service.description,\n      basePrice: service.basePrice,\n      category: service.category,\n      allowPriceOverride: service.allowPriceOverride,\n      bundledServices: service.bundledServices || [],\n      isActive: service.isActive\n    });\n    setEditingService(service);\n    setShowCreateModal(true);\n  };\n\n  if (!hasPermission('admin')) {\n    return (\n      <div className=\"text-center py-12\">\n        <Monitor className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Access Denied</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          You don't have permission to manage services.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center\">\n            <Monitor className=\"h-6 w-6 text-primary-600 mr-2\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">Services Management</h1>\n          </div>\n          <button\n            onClick={() => {\n              resetForm();\n              setEditingService(null);\n              setShowCreateModal(true);\n            }}\n            className=\"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center\"\n          >\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Add Service\n          </button>\n        </div>\n\n        {error && (\n          <div className=\"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n            {error}\n          </div>\n        )}\n\n        {/* Search and Filter */}\n        <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search services...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n            />\n          </div>\n          <div className=\"relative\">\n            <Filter className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n            >\n              <option value=\"\">All Categories</option>\n              {categories.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Services Grid */}\n        {loading ? (\n          <div className=\"text-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"></div>\n            <p className=\"mt-2 text-gray-500\">Loading services...</p>\n          </div>\n        ) : filteredServices.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <Monitor className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No services found</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchTerm || selectedCategory ? 'Try adjusting your search or filter.' : 'Get started by creating your first service.'}\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredServices.map((service) => (\n              <ServiceCard\n                key={service.id}\n                service={service}\n                onEdit={openEditModal}\n                onDelete={handleDeleteService}\n                onToggleActive={handleToggleActive}\n                allServices={services}\n              />\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Create/Edit Modal */}\n      {showCreateModal && (\n        <ServiceModal\n          service={editingService}\n          formData={formData}\n          setFormData={setFormData}\n          onSubmit={editingService ? handleUpdateService : handleCreateService}\n          onClose={() => {\n            setShowCreateModal(false);\n            setEditingService(null);\n            resetForm();\n          }}\n          allServices={services}\n        />\n      )}\n    </div>\n  );\n};\n\n// Service Card Component\ninterface ServiceCardProps {\n  service: Service;\n  onEdit: (service: Service) => void;\n  onDelete: (serviceId: string) => void;\n  onToggleActive: (service: Service) => void;\n  allServices: Service[];\n}\n\nconst ServiceCard: React.FC<ServiceCardProps> = ({\n  service,\n  onEdit,\n  onDelete,\n  onToggleActive,\n  allServices\n}) => {\n  const bundledServiceNames = service.bundledServices\n    ?.map(id => allServices.find(s => s.id === id)?.name)\n    .filter(Boolean) || [];\n\n  return (\n    <div className={`bg-white border rounded-lg p-4 ${service.isActive ? 'border-gray-200' : 'border-gray-300 bg-gray-50'}`}>\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex-1\">\n          <h3 className={`font-semibold ${service.isActive ? 'text-gray-900' : 'text-gray-500'}`}>\n            {service.name}\n          </h3>\n          <p className={`text-sm ${service.isActive ? 'text-gray-600' : 'text-gray-400'}`}>\n            {service.description}\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => onEdit(service)}\n            className=\"text-blue-600 hover:text-blue-800\"\n          >\n            <Edit className=\"h-4 w-4\" />\n          </button>\n          <button\n            onClick={() => onDelete(service.id)}\n            className=\"text-red-600 hover:text-red-800\"\n          >\n            <Trash2 className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n\n      <div className=\"space-y-2\">\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-sm text-gray-500\">Category:</span>\n          <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">\n            {service.category}\n          </span>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-sm text-gray-500\">Base Price:</span>\n          <span className=\"font-semibold text-green-600\">\n            KSh {service.basePrice.toLocaleString()}\n          </span>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-sm text-gray-500\">Price Override:</span>\n          <span className={`text-xs ${service.allowPriceOverride ? 'text-green-600' : 'text-red-600'}`}>\n            {service.allowPriceOverride ? 'Allowed' : 'Not Allowed'}\n          </span>\n        </div>\n\n        {bundledServiceNames.length > 0 && (\n          <div className=\"mt-2\">\n            <span className=\"text-sm text-gray-500\">Bundled Services:</span>\n            <div className=\"mt-1 flex flex-wrap gap-1\">\n              {bundledServiceNames.map((name, index) => (\n                <span key={index} className=\"inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-800 rounded\">\n                  <Link className=\"h-3 w-3 mr-1\" />\n                  {name}\n                </span>\n              ))}\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex items-center justify-between pt-2 border-t\">\n          <span className=\"text-sm text-gray-500\">Status:</span>\n          <button\n            onClick={() => onToggleActive(service)}\n            className=\"flex items-center\"\n          >\n            {service.isActive ? (\n              <ToggleRight className=\"h-5 w-5 text-green-500\" />\n            ) : (\n              <ToggleLeft className=\"h-5 w-5 text-gray-400\" />\n            )}\n            <span className={`ml-1 text-sm ${service.isActive ? 'text-green-600' : 'text-gray-500'}`}>\n              {service.isActive ? 'Active' : 'Inactive'}\n            </span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Services;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,MAAM,EAGNC,UAAU,EACVC,WAAW,EACXC,IAAI,QAGC,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,QAAQ,yBAAyB;AAErD,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAc,CAAC,GAAGP,OAAO,CAAC,CAAC;EACnC,MAAM;IACJQ,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,aAAa;IACbC,aAAa;IACbC,aAAa;IACbC;EACF,CAAC,GAAGb,WAAW,CAAC,CAAC;EAEjB,MAAM,CAACc,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC;IACvCmC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,EAAE;IACZC,kBAAkB,EAAE,IAAI;IACxBC,eAAe,EAAE,EAAc;IAC/BC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAGxB,QAAQ,CAACyB,MAAM,CAACC,OAAO,IAAI;IAClD,MAAMC,aAAa,GAAGD,OAAO,CAACT,IAAI,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClB,UAAU,CAACiB,WAAW,CAAC,CAAC,CAAC,IAC9DF,OAAO,CAACR,WAAW,CAACU,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClB,UAAU,CAACiB,WAAW,CAAC,CAAC,CAAC;IACzF,MAAME,eAAe,GAAG,CAACjB,gBAAgB,IAAIa,OAAO,CAACN,QAAQ,KAAKP,gBAAgB;IAClF,OAAOc,aAAa,IAAIG,eAAe;EACzC,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAGzB,oBAAoB,CAAC,CAAC;EAEzC,MAAM0B,mBAAmB,GAAG,MAAOC,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAM/B,aAAa,CAACY,QAAQ,CAAC;MAC7BP,kBAAkB,CAAC,KAAK,CAAC;MACzB2B,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMmC,mBAAmB,GAAG,MAAOJ,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACzB,cAAc,EAAE;IAErB,IAAI;MACF,MAAML,aAAa,CAACK,cAAc,CAAC6B,EAAE,EAAEvB,QAAQ,CAAC;MAChDL,iBAAiB,CAAC,IAAI,CAAC;MACvByB,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMqC,mBAAmB,GAAG,MAAOC,SAAiB,IAAK;IACvD,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAMrC,aAAa,CAACmC,SAAS,CAAC;MAChC,CAAC,CAAC,OAAOtC,KAAK,EAAE;QACdkC,OAAO,CAAClC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF;EACF,CAAC;EAED,MAAMyC,kBAAkB,GAAG,MAAOjB,OAAgB,IAAK;IACrD,IAAI;MACF,MAAMtB,aAAa,CAACsB,OAAO,CAACY,EAAE,EAAE;QAAEf,QAAQ,EAAE,CAACG,OAAO,CAACH;MAAS,CAAC,CAAC;IAClE,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAMiC,SAAS,GAAGA,CAAA,KAAM;IACtBnB,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,EAAE;MACZC,kBAAkB,EAAE,IAAI;MACxBC,eAAe,EAAE,EAAE;MACnBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMqB,aAAa,GAAIlB,OAAgB,IAAK;IAC1CV,WAAW,CAAC;MACVC,IAAI,EAAES,OAAO,CAACT,IAAI;MAClBC,WAAW,EAAEQ,OAAO,CAACR,WAAW;MAChCC,SAAS,EAAEO,OAAO,CAACP,SAAS;MAC5BC,QAAQ,EAAEM,OAAO,CAACN,QAAQ;MAC1BC,kBAAkB,EAAEK,OAAO,CAACL,kBAAkB;MAC9CC,eAAe,EAAEI,OAAO,CAACJ,eAAe,IAAI,EAAE;MAC9CC,QAAQ,EAAEG,OAAO,CAACH;IACpB,CAAC,CAAC;IACFb,iBAAiB,CAACgB,OAAO,CAAC;IAC1BlB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAI,CAACT,aAAa,CAAC,OAAO,CAAC,EAAE;IAC3B,oBACEH,OAAA;MAAKiD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChClD,OAAA,CAACb,OAAO;QAAC8D,SAAS,EAAC;MAAiC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvDtD,OAAA;QAAIiD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzEtD,OAAA;QAAGiD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV;EAEA,oBACEtD,OAAA;IAAKiD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBlD,OAAA;MAAKiD,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7ClD,OAAA;QAAKiD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlD,OAAA;UAAKiD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClD,OAAA,CAACb,OAAO;YAAC8D,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDtD,OAAA;YAAIiD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACNtD,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAM;YACbhB,SAAS,CAAC,CAAC;YACXzB,iBAAiB,CAAC,IAAI,CAAC;YACvBF,kBAAkB,CAAC,IAAI,CAAC;UAC1B,CAAE;UACFqC,SAAS,EAAC,uFAAuF;UAAAC,QAAA,gBAEjGlD,OAAA,CAACZ,IAAI;YAAC6D,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELhD,KAAK,iBACJN,OAAA;QAAKiD,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjF5C;MAAK;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDtD,OAAA;QAAKiD,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDlD,OAAA;UAAKiD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BlD,OAAA,CAACT,MAAM;YAAC0D,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FtD,OAAA;YACEwD,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oBAAoB;YAChCC,KAAK,EAAE3C,UAAW;YAClB4C,QAAQ,EAAGtB,CAAC,IAAKrB,aAAa,CAACqB,CAAC,CAACuB,MAAM,CAACF,KAAK,CAAE;YAC/CT,SAAS,EAAC;UAA0G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtD,OAAA;UAAKiD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBlD,OAAA,CAACR,MAAM;YAACyD,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FtD,OAAA;YACE0D,KAAK,EAAEzC,gBAAiB;YACxB0C,QAAQ,EAAGtB,CAAC,IAAKnB,mBAAmB,CAACmB,CAAC,CAACuB,MAAM,CAACF,KAAK,CAAE;YACrDT,SAAS,EAAC,mGAAmG;YAAAC,QAAA,gBAE7GlD,OAAA;cAAQ0D,KAAK,EAAC,EAAE;cAAAR,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACvCnB,UAAU,CAAC0B,GAAG,CAACrC,QAAQ,iBACtBxB,OAAA;cAAuB0D,KAAK,EAAElC,QAAS;cAAA0B,QAAA,EAAE1B;YAAQ,GAApCA,QAAQ;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqC,CAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLjD,OAAO,gBACNL,OAAA;QAAKiD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlD,OAAA;UAAKiD,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FtD,OAAA;UAAGiD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,GACJ1B,gBAAgB,CAACkC,MAAM,KAAK,CAAC,gBAC/B9D,OAAA;QAAKiD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClD,OAAA,CAACb,OAAO;UAAC8D,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDtD,OAAA;UAAIiD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EtD,OAAA;UAAGiD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACtCnC,UAAU,IAAIE,gBAAgB,GAAG,sCAAsC,GAAG;QAA6C;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAENtD,OAAA;QAAKiD,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEtB,gBAAgB,CAACiC,GAAG,CAAE/B,OAAO,iBAC5B9B,OAAA,CAAC+D,WAAW;UAEVjC,OAAO,EAAEA,OAAQ;UACjBkC,MAAM,EAAEhB,aAAc;UACtBiB,QAAQ,EAAEtB,mBAAoB;UAC9BuB,cAAc,EAAEnB,kBAAmB;UACnCoB,WAAW,EAAE/D;QAAS,GALjB0B,OAAO,CAACY,EAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMhB,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL3C,eAAe,iBACdX,OAAA,CAACF,YAAY;MACXgC,OAAO,EAAEjB,cAAe;MACxBM,QAAQ,EAAEA,QAAS;MACnBC,WAAW,EAAEA,WAAY;MACzBgD,QAAQ,EAAEvD,cAAc,GAAG4B,mBAAmB,GAAGL,mBAAoB;MACrEiC,OAAO,EAAEA,CAAA,KAAM;QACbzD,kBAAkB,CAAC,KAAK,CAAC;QACzBE,iBAAiB,CAAC,IAAI,CAAC;QACvByB,SAAS,CAAC,CAAC;MACb,CAAE;MACF4B,WAAW,EAAE/D;IAAS;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAApD,EAAA,CA5NMD,QAAkB;EAAA,QACIL,OAAO,EAS7BC,WAAW;AAAA;AAAAyE,EAAA,GAVXrE,QAAkB;AAqOxB,MAAM8D,WAAuC,GAAGA,CAAC;EAC/CjC,OAAO;EACPkC,MAAM;EACNC,QAAQ;EACRC,cAAc;EACdC;AACF,CAAC,KAAK;EAAA,IAAAI,qBAAA;EACJ,MAAMC,mBAAmB,GAAG,EAAAD,qBAAA,GAAAzC,OAAO,CAACJ,eAAe,cAAA6C,qBAAA,uBAAvBA,qBAAA,CACxBV,GAAG,CAACnB,EAAE;IAAA,IAAA+B,iBAAA;IAAA,QAAAA,iBAAA,GAAIN,WAAW,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAKA,EAAE,CAAC,cAAA+B,iBAAA,uBAAlCA,iBAAA,CAAoCpD,IAAI;EAAA,EAAC,CACpDQ,MAAM,CAAC+C,OAAO,CAAC,KAAI,EAAE;EAExB,oBACE5E,OAAA;IAAKiD,SAAS,EAAE,kCAAkCnB,OAAO,CAACH,QAAQ,GAAG,iBAAiB,GAAG,4BAA4B,EAAG;IAAAuB,QAAA,gBACtHlD,OAAA;MAAKiD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDlD,OAAA;QAAKiD,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBlD,OAAA;UAAIiD,SAAS,EAAE,iBAAiBnB,OAAO,CAACH,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;UAAAuB,QAAA,EACpFpB,OAAO,CAACT;QAAI;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACLtD,OAAA;UAAGiD,SAAS,EAAE,WAAWnB,OAAO,CAACH,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;UAAAuB,QAAA,EAC7EpB,OAAO,CAACR;QAAW;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNtD,OAAA;QAAKiD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClD,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAMS,MAAM,CAAClC,OAAO,CAAE;UAC/BmB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7ClD,OAAA,CAACX,IAAI;YAAC4D,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACTtD,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAMU,QAAQ,CAACnC,OAAO,CAACY,EAAE,CAAE;UACpCO,SAAS,EAAC,iCAAiC;UAAAC,QAAA,eAE3ClD,OAAA,CAACV,MAAM;YAAC2D,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtD,OAAA;MAAKiD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBlD,OAAA;QAAKiD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDlD,OAAA;UAAMiD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDtD,OAAA;UAAMiD,SAAS,EAAC,oFAAoF;UAAAC,QAAA,EACjGpB,OAAO,CAACN;QAAQ;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENtD,OAAA;QAAKiD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDlD,OAAA;UAAMiD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1DtD,OAAA;UAAMiD,SAAS,EAAC,8BAA8B;UAAAC,QAAA,GAAC,MACzC,EAACpB,OAAO,CAACP,SAAS,CAACsD,cAAc,CAAC,CAAC;QAAA;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENtD,OAAA;QAAKiD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDlD,OAAA;UAAMiD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9DtD,OAAA;UAAMiD,SAAS,EAAE,WAAWnB,OAAO,CAACL,kBAAkB,GAAG,gBAAgB,GAAG,cAAc,EAAG;UAAAyB,QAAA,EAC1FpB,OAAO,CAACL,kBAAkB,GAAG,SAAS,GAAG;QAAa;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAELkB,mBAAmB,CAACV,MAAM,GAAG,CAAC,iBAC7B9D,OAAA;QAAKiD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBlD,OAAA;UAAMiD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChEtD,OAAA;UAAKiD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EACvCsB,mBAAmB,CAACX,GAAG,CAAC,CAACxC,IAAI,EAAEyD,KAAK,kBACnC9E,OAAA;YAAkBiD,SAAS,EAAC,gFAAgF;YAAAC,QAAA,gBAC1GlD,OAAA,CAACL,IAAI;cAACsD,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChCjC,IAAI;UAAA,GAFIyD,KAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDtD,OAAA;QAAKiD,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC9DlD,OAAA;UAAMiD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtDtD,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAMW,cAAc,CAACpC,OAAO,CAAE;UACvCmB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAE5BpB,OAAO,CAACH,QAAQ,gBACf3B,OAAA,CAACN,WAAW;YAACuD,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAElDtD,OAAA,CAACP,UAAU;YAACwD,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAChD,eACDtD,OAAA;YAAMiD,SAAS,EAAE,gBAAgBnB,OAAO,CAACH,QAAQ,GAAG,gBAAgB,GAAG,eAAe,EAAG;YAAAuB,QAAA,EACtFpB,OAAO,CAACH,QAAQ,GAAG,QAAQ,GAAG;UAAU;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACyB,GAAA,GA7FIhB,WAAuC;AA+F7C,eAAe9D,QAAQ;AAAC,IAAAqE,EAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}